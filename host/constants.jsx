/**
 * Constants for SahAI CEP Extension - ExtendScript Side
 * This file contains shared constants between the CEP panel and ExtendScript
 * 
 * NOTE: This file is no longer used as its content has been inlined into ae-integration.jsxinc
 * to avoid issues with #include statements in ExtendScript.
 */

// File paths - Using global variables instead of var for better scope
if (typeof SETTINGS_FILE_PATH === 'undefined') {
    SETTINGS_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/settings.json";
}
if (typeof HISTORY_FILE_PATH === 'undefined') {
    HISTORY_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/history.json";
}
if (typeof EXTENSION_DIR_PATH === 'undefined') {
    EXTENSION_DIR_PATH = "~/Adobe/CEP/extensions/SahAI";
}

// API defaults - Using global variables instead of var for better scope
if (typeof DEFAULT_OLLAMA_URL === 'undefined') {
    DEFAULT_OLLAMA_URL = "http://localhost:11434";
}

// HTTP settings
if (typeof DEFAULT_TIMEOUT === 'undefined') {
    DEFAULT_TIMEOUT = 10000;
}
if (typeof DEFAULT_RETRIES === 'undefined') {
    DEFAULT_RETRIES = 2;
}

// Port numbers
if (typeof OLLAMA_DEFAULT_PORT === 'undefined') {
    OLLAMA_DEFAULT_PORT = 11434;
}
if (typeof HTTPS_DEFAULT_PORT === 'undefined') {
    HTTPS_DEFAULT_PORT = 443;
}

// Context lengths (common defaults)
if (typeof DEFAULT_CONTEXT_LENGTH === 'undefined') {
    DEFAULT_CONTEXT_LENGTH = 4096;
}
if (typeof SMALL_CONTEXT_LENGTH === 'undefined') {
    SMALL_CONTEXT_LENGTH = 8192;
}
if (typeof MEDIUM_CONTEXT_LENGTH === 'undefined') {
    MEDIUM_CONTEXT_LENGTH = 32000;
}
if (typeof LARGE_CONTEXT_LENGTH === 'undefined') {
    LARGE_CONTEXT_LENGTH = 128000;
}
if (typeof EXTRA_LARGE_CONTEXT_LENGTH === 'undefined') {
    EXTRA_LARGE_CONTEXT_LENGTH = 200000;
}

// Note: ExtendScript doesn't support CommonJS modules
// Constants are available globally when this file is included
