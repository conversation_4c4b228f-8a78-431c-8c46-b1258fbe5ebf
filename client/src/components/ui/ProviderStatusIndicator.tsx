import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { ProviderStatusChecker } from '../../utils/cepIntegration';

export const ProviderStatusIndicator: React.FC = () => {
  const { getActiveProvider } = useSettingsStore();
  const [status, setStatus] = useState<{
    isOnline: boolean | null;
    latency?: number;
    isChecking: boolean;
    error?: string;
  }>({
    isOnline: null,
    isChecking: false,
  });

  const activeProvider = getActiveProvider();

  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;
    let isMounted = true;

    const checkStatus = async () => {
      if (!activeProvider?.isConfigured) {
        if (isMounted) {
          setStatus({ isOnline: null, isChecking: false });
        }
        return;
      }

      if (isMounted) {
        setStatus(prev => ({ ...prev, isChecking: true, error: undefined }));
      }
      try {
        const result = await ProviderStatusChecker.checkProviderStatus(
          activeProvider.id,
          { apiKey: activeProvider.apiKey, baseURL: activeProvider.baseURL }
        );
        if (isMounted) {
          setStatus({
            isOnline: result.isOnline,
            latency: result.latency,
            isChecking: false,
          });
        }
      } catch (error: any) {
        if (isMounted) {
          setStatus({
            isOnline: false,
            isChecking: false,
            error: error.message,
          });
        }
      }
    };

    if (activeProvider?.isConfigured) {
      checkStatus();
      timeoutId = setInterval(checkStatus, 30_000); // 30-second poll
    }

    return () => {
      isMounted = false;
      if (timeoutId) clearInterval(timeoutId);
    };
  }, [activeProvider]);

  const getDotClasses = () => {
    const baseClasses = 'w-2.5 h-2.5 rounded-full transition-all duration-300 shadow-sm hover:scale-110 cursor-pointer';
    if (status.isChecking) {
      return `${baseClasses} bg-adobe-warning animate-pulse shadow-adobe-warning/40`;
    }
    if (status.isOnline === true) {
      return `${baseClasses} bg-adobe-success shadow-adobe-success/40`;
    }
    return `${baseClasses} bg-adobe-error shadow-adobe-error/40`;
  };

  return (
    <div className={getDotClasses()} />
  );
};
