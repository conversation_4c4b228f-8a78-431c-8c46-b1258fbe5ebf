import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { CEPSettings, ProviderBridge } from '../utils/cepIntegration';
import { toast } from '../components/stores/toastStore';
import { SUCCESS_TOAST_DURATION, ERROR_TOAST_DURATION } from '../utils/constants';
import { processError, formatErrorForLogging } from '../utils/errorHandling';

export interface Model {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  capabilities?: string[];
  isRecommended?: boolean;
}

interface Provider {
  id: string;
  name: string;
  isConfigured: boolean;
  configType: 'apiKey' | 'baseURL' | 'other';
  apiKey?: string;
  baseURL?: string;
  models: Model[];
  selectedModelId?: string;
  isLoading?: boolean;
  error?: string;
  settings?: Record<string, unknown>;
}

interface SettingsState {
  providers: Provider[];
  activeProviderId: string | undefined;
  isLoadingModels: boolean;

  // Provider actions
  setActiveProvider: (providerId: string) => void;
  updateProviderConfig: (providerId: string, config: Partial<Provider>) => void;
  setProviderModels: (providerId: string, models: Model[]) => void;
  setSelectedModel: (providerId: string, modelId: string) => void;
  updateProviderKey: (providerId: string, apiKey: string, selectedModelId?: string) => void;
  saveProviderSelection: (providerId: string, config: Partial<Provider>) => void;

  // Model actions
  loadModelsForProvider: (providerId: string) => Promise<void>;

  // CEP integration
  persistSettings: () => void;
  loadSettings: () => Promise<void>;

  // Computed getters
  getActiveProvider: () => Provider | null;
  getActiveModel: () => Model | null;
}

// Helper functions for model loading
const validateProviderConfig = (provider: Provider): string | null => {
  if (provider.configType === 'baseURL' && !provider.baseURL?.trim()) {
    return 'Base URL required';
  } else if (provider.configType === 'apiKey' && !provider.apiKey?.trim()) {
    return 'API Key required';
  }
  return null;
};

const transformModelsData = (models: any[], _providerId: string): Model[] => {
  if (!Array.isArray(models)) {
    console.warn('Invalid models data received:', models);
    return [];
  }
  
  return models.map((m: any) => ({
    id: m.id || 'unknown-id',
    name: m.name || m.id || 'Unknown Model',
    description: m.description || '',
    contextLength: m.contextLength || m.context_length || 4096,
    isRecommended: m.isRecommended || m.is_recommended || false
  }));
};

export const useSettingsStore = create<SettingsState>()(
  subscribeWithSelector((set, get) => ({
  providers: [
    { id: 'openai', name: 'OpenAI', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'anthropic', name: 'Anthropic', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'gemini', name: 'Google Gemini', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'groq', name: 'Groq', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'deepseek', name: 'DeepSeek', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'openrouter', name: 'OpenRouter', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'ollama', name: 'Ollama', configType: 'baseURL', isConfigured: false, models: [] },
  ],
  activeProviderId: 'openai',
  isLoadingModels: false,

  setActiveProvider: (providerId) => {
    set(state => ({
      activeProviderId: providerId,
      providers: state.providers.map(p =>
        p.id !== providerId ? { ...p, models: [], isLoading: false, error: undefined } : p
      )
    }));
    get().persistSettings();

    const provider = get().providers.find(p => p.id === providerId);
    if (provider && provider.isConfigured && (provider.apiKey || provider.baseURL) && providerId !== 'ollama') {
      get().loadModelsForProvider(providerId);
    }
  },

  updateProviderConfig: (providerId, config) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, ...config, isConfigured: !!(config.apiKey?.trim() || config.baseURL?.trim()) } : p
      )
    }));
    get().persistSettings();
    
    if ((config.apiKey?.trim() || config.baseURL?.trim()) && providerId !== 'ollama') {
      get().loadModelsForProvider(providerId);
    }
  },

  setProviderModels: (providerId, models) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, models, isLoading: false, error: undefined } : p
      )
    }));
  },

  setSelectedModel: (providerId, modelId) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, selectedModelId: modelId } : p
      )
    }));
    get().persistSettings();
  },

  updateProviderKey: (providerId, apiKey, selectedModelId) => {
    set(state => ({
      providers: state.providers.map(p =>
        p.id === providerId
          ? { ...p, apiKey, isConfigured: !!apiKey?.trim(), selectedModelId: selectedModelId || p.selectedModelId }
          : p
      )
    }));
    get().persistSettings();
    
    if (apiKey?.trim() && providerId !== 'ollama') {
      get().loadModelsForProvider(providerId);
    }
  },

  saveProviderSelection: (providerId, config) => {
    const provider = get().providers.find(p => p.id === providerId);

    set(state => ({
      activeProviderId: providerId,
      providers: state.providers.map(p =>
        p.id === providerId
          ? { ...p, ...config, isConfigured: !!(config.apiKey?.trim() || config.baseURL?.trim()) }
          : p
      )
    }));
    get().persistSettings();

    toast.success(
      'Provider configured',
      `${provider?.name || providerId} has been configured successfully`,
      SUCCESS_TOAST_DURATION
    );

    if ((config.apiKey?.trim() || config.baseURL?.trim()) && providerId !== 'ollama') {
      get().loadModelsForProvider(providerId);
    }
  },

  loadModelsForProvider: async (providerId) => {
    const provider = get().providers.find(p => p.id === providerId);
    if (!provider) return;

    // Remove the faulty activeProviderId check that was preventing model loading
    // The modal should be able to load models for any provider being configured

    if (provider.isLoading) {
      console.log(`Already loading models for ${providerId}, skipping...`);
      return;
    }

    // Special handling for Ollama - validate connection first
    if (providerId === 'ollama') {
      const baseURL = provider.baseURL || 'http://localhost:11434';
      try {
        // Validate Ollama connection before proceeding
        const validationUrl = `${baseURL.replace(/\/$/, '')}/api/tags`;
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
        
        const response = await fetch(validationUrl, { 
          method: 'GET', 
          signal: controller.signal 
        });
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new Error(`Ollama service not accessible at ${baseURL}`);
        }
      } catch (error: any) {
        const errorMessage = error.name === 'AbortError' 
          ? 'Ollama connection timed out. Ensure it is running.' 
          : 'Cannot connect to Ollama service. Ensure it is running.';
          
        set(state => ({
          providers: state.providers.map(p =>
            p.id === providerId ? { 
              ...p, 
              isLoading: false, 
              error: errorMessage
            } : p
          )
        }));
        
        toast.error(
          'Ollama Connection Failed',
          errorMessage,
          ERROR_TOAST_DURATION
        );
        return;
      }
    }

    const validationError = validateProviderConfig(provider);
    if (validationError) {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, error: validationError } : p
        )
      }));
      return;
    }

    set(state => ({
      providers: state.providers.map(p =>
        p.id === providerId ? { ...p, isLoading: true, error: undefined } : p
      )
    }));

    try {
      // Use ProviderBridge from cepIntegration (already imported at the top)
      const models = await ProviderBridge.listModels(providerId, provider.baseURL, provider.apiKey);
      
      if (!Array.isArray(models)) {
        throw new Error('Invalid response format from provider');
      }

      const transformedModels = transformModelsData(models, providerId);

      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, models: transformedModels, isLoading: false, error: undefined } : p
        )
      }));

      if (transformedModels.length > 0 && !provider.selectedModelId) {
        set(state => ({
          providers: state.providers.map(p =>
            p.id === providerId ? { ...p, selectedModelId: transformedModels[0].id } : p
          )
        }));
      }

      toast.success(
        'Models loaded successfully',
        `Found ${transformedModels.length} models for ${provider.name}`,
        SUCCESS_TOAST_DURATION
      );
    } catch (error: any) {
      const errorInfo = processError(error);
      console.error(formatErrorForLogging(error, `loadModelsForProvider:${providerId}`));

      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, isLoading: false, error: errorInfo.userMessage } : p
        )
      }));

      const providerName = provider?.name || providerId;
      toast.error(
        `Failed to load ${providerName} models`,
        errorInfo.userMessage,
        ERROR_TOAST_DURATION
      );
    }
  },

  persistSettings: () => {
    const { activeProviderId, providers } = get();
    CEPSettings.save({
      activeProviderId,
      providers: providers.map(p => ({
        id: p.id,
        isConfigured: p.isConfigured,
        configType: p.configType,
        apiKey: p.apiKey,
        baseURL: p.baseURL,
        selectedModelId: p.selectedModelId,
        settings: p.settings
      }))
    });
  },

  loadSettings: async () => {
    try {
      const settings = await CEPSettings.load();
      const activeProviderId = settings.activeProviderId || 'openai';

      set({ activeProviderId });

      if (settings.providers && Array.isArray(settings.providers)) {
        set(state => ({
          providers: state.providers.map(p => {
            const saved = settings.providers?.find((sp: any) => sp.id === p.id);
            return saved ? { ...p, ...saved } : p;
          })
        }));
      }

      const state = get();
      const activeProvider = state.providers.find(p => p.id === state.activeProviderId);

      if (activeProvider && activeProvider.isConfigured &&
          (activeProvider.apiKey || activeProvider.baseURL) && activeProvider.id !== 'ollama') {
        // Use setTimeout to ensure the store is fully updated before loading models
        setTimeout(() => {
          get().loadModelsForProvider(activeProvider.id);
        }, 0);
      }
    } catch (error) {
      console.error('Failed to load CEP settings:', error);
      set({ activeProviderId: 'openai' });
    }
  },

  getActiveProvider: () => {
    const { providers, activeProviderId } = get();
    return providers.find(p => p.id === activeProviderId) || null;
  },

  getActiveModel: () => {
    const activeProvider = get().getActiveProvider();
    if (!activeProvider?.selectedModelId) return null;
    return activeProvider.models.find(m => m.id === activeProvider.selectedModelId) || null;
  }
})));
