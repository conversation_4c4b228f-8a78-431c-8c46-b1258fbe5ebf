// SahAI CEP Extension V2 - ExtendScript Main File
// This file handles communication between the CEP panel and Adobe applications

// Import constants and model data

// BEGIN: Inlined constants.jsx
// Constants for SahAI CEP Extension - ExtendScript Side
// This file contains shared constants between the CEP panel and ExtendScript

// File paths - Using global variables instead of var for better scope
if (typeof SETTINGS_FILE_PATH === 'undefined') {
    SETTINGS_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/settings.json";
}
if (typeof HISTORY_FILE_PATH === 'undefined') {
    HISTORY_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/history.json";
}
if (typeof EXTENSION_DIR_PATH === 'undefined') {
    EXTENSION_DIR_PATH = "~/Adobe/CEP/extensions/SahAI";
}

// API defaults - Using global variables instead of var for better scope
if (typeof DEFAULT_OLLAMA_URL === 'undefined') {
    DEFAULT_OLLAMA_URL = "http://localhost:11434";
}

// HTTP settings
if (typeof DEFAULT_TIMEOUT === 'undefined') {
    DEFAULT_TIMEOUT = 10000;
}
if (typeof DEFAULT_RETRIES === 'undefined') {
    DEFAULT_RETRIES = 2;
}

// Port numbers
if (typeof OLLAMA_DEFAULT_PORT === 'undefined') {
    OLLAMA_DEFAULT_PORT = 11434;
}
if (typeof HTTPS_DEFAULT_PORT === 'undefined') {
    HTTPS_DEFAULT_PORT = 443;
}

// Context lengths (common defaults)
if (typeof DEFAULT_CONTEXT_LENGTH === 'undefined') {
    DEFAULT_CONTEXT_LENGTH = 4096;
}
if (typeof SMALL_CONTEXT_LENGTH === 'undefined') {
    SMALL_CONTEXT_LENGTH = 8192;
}
if (typeof MEDIUM_CONTEXT_LENGTH === 'undefined') {
    MEDIUM_CONTEXT_LENGTH = 32000;
}
if (typeof LARGE_CONTEXT_LENGTH === 'undefined') {
    LARGE_CONTEXT_LENGTH = 128000;
}
if (typeof EXTRA_LARGE_CONTEXT_LENGTH === 'undefined') {
    EXTRA_LARGE_CONTEXT_LENGTH = 200000;
}

// Note: ExtendScript doesn't support CommonJS modules
// Constants are available globally when this file is included
// END: Inlined constants.jsx

// BEGIN: Inlined modelData.jsx
// Shared model data for ExtendScript host
// This file serves as the single source of truth for model information
// Used by host scripts - JavaScript version of client/src/utils/modelData.ts

// Ensure constants are available
if (typeof DEFAULT_CONTEXT_LENGTH === 'undefined') {
    DEFAULT_CONTEXT_LENGTH = 4096;
}
if (typeof SMALL_CONTEXT_LENGTH === 'undefined') {
    SMALL_CONTEXT_LENGTH = 8192;
}
if (typeof MEDIUM_CONTEXT_LENGTH === 'undefined') {
    MEDIUM_CONTEXT_LENGTH = 32000;
}
if (typeof LARGE_CONTEXT_LENGTH === 'undefined') {
    LARGE_CONTEXT_LENGTH = 128000;
}
if (typeof EXTRA_LARGE_CONTEXT_LENGTH === 'undefined') {
    EXTRA_LARGE_CONTEXT_LENGTH = 200000;
}

// Fallback model data
var FALLBACK_MODELS = {
  openai: [
    { 
      id: 'gpt-4o', 
      name: 'GPT-4o', 
      description: 'Most capable OpenAI model', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'gpt-4o-mini', 
      name: 'GPT-4o Mini', 
      description: 'Faster, more affordable', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'gpt-4-turbo', 
      name: 'GPT-4 Turbo', 
      description: 'Previous generation flagship', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'gpt-3.5-turbo', 
      name: 'GPT-3.5 Turbo', 
      description: 'Legacy model', 
      context_length: 16384, 
      is_recommended: false 
    }
  ],
  
  anthropic: [
    { 
      id: 'claude-3-5-sonnet-20241022', 
      name: 'Claude 3.5 Sonnet', 
      description: 'Anthropic\'s most capable model', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'claude-3-5-haiku-20241022', 
      name: 'Claude 3.5 Haiku', 
      description: 'Fast and efficient', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'claude-3-opus-20240229', 
      name: 'Claude 3 Opus', 
      description: 'Powerful reasoning', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'claude-2.1', 
      name: 'Claude 2.1', 
      description: 'Previous generation', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  gemini: [
    { 
      id: 'gemini-1.5-pro', 
      name: 'Gemini 1.5 Pro', 
      description: 'Google\'s most capable model', 
      context_length: 2000000, 
      is_recommended: true 
    },
    { 
      id: 'gemini-1.5-flash', 
      name: 'Gemini 1.5 Flash', 
      description: 'Fast and efficient', 
      context_length: 1000000, 
      is_recommended: false 
    },
    { 
      id: 'gemini-1.0-pro', 
      name: 'Gemini 1.0 Pro', 
      description: 'Previous generation', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  groq: [
    { 
      id: 'llama-3.1-70b-versatile', 
      name: 'Llama 3.1 70B', 
      description: 'Balanced performance', 
      context_length: 131072, 
      is_recommended: true 
    },
    { 
      id: 'llama-3.1-8b-instant', 
      name: 'Llama 3.1 8B', 
      description: 'Fast inference', 
      context_length: 131072, 
      is_recommended: false 
    },
    { 
      id: 'mixtral-8x7b-32768', 
      name: 'Mixtral 8x7B', 
      description: 'Large context', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  deepseek: [
    { 
      id: 'deepseek-chat', 
      name: 'DeepSeek Chat', 
      description: 'General purpose', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'deepseek-coder', 
      name: 'DeepSeek Coder', 
      description: 'Code-focused', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],

  openrouter: [
    { 
      id: 'openai/gpt-4o', 
      name: 'GPT-4o (OpenRouter)', 
      description: 'OpenAI via OpenRouter', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'anthropic/claude-3.5-sonnet', 
      name: 'Claude 3.5 Sonnet (OpenRouter)', 
      description: 'Anthropic via OpenRouter', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'meta-llama/llama-3.1-70b-instruct', 
      name: 'Llama 3.1 70B (OpenRouter)', 
      description: 'Meta via OpenRouter', 
      context_length: 131072, 
      is_recommended: false 
    }
  ],

  ollama: [
    { 
      id: 'llama3.1', 
      name: 'Llama 3.1', 
      description: 'Open source LLM', 
      context_length: DEFAULT_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'mistral', 
      name: 'Mistral', 
      description: 'Efficient transformer', 
      context_length: SMALL_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'codellama', 
      name: 'Code Llama', 
      description: 'Code-focused', 
      context_length: 16384, 
      is_recommended: false 
    }
  ]
};

// Get fallback models for a specific provider
function getFallbackModels(providerId) {
  return FALLBACK_MODELS[providerId] || [];
}

// Get model description by provider and model ID
function getModelDescription(providerId, modelId) {
  var models = FALLBACK_MODELS[providerId] || [];
  for (var i = 0; i < models.length; i++) {
    if (models[i].id === modelId) {
      return models[i].description;
    }
  }
  return '';
}

// Get model context length by provider and model ID
function getModelContextLength(providerId, modelId) {
  var models = FALLBACK_MODELS[providerId] || [];
  for (var i = 0; i < models.length; i++) {
    if (models[i].id === modelId) {
      return models[i].context_length;
    }
  }
  return DEFAULT_CONTEXT_LENGTH;
}

// Check if a model is recommended for a provider
function isModelRecommended(providerId, modelId) {
  var models = FALLBACK_MODELS[providerId] || [];
  for (var i = 0; i < models.length; i++) {
    if (models[i].id === modelId) {
      return models[i].is_recommended;
    }
  }
  return false;
}
// END: Inlined modelData.jsx

// Ensure constants are properly defined
if (typeof SETTINGS_FILE_PATH === 'undefined') {
    SETTINGS_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/settings.json";
}
if (typeof HISTORY_FILE_PATH === 'undefined') {
    HISTORY_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/history.json";
}
if (typeof EXTENSION_DIR_PATH === 'undefined') {
    EXTENSION_DIR_PATH = "~/Adobe/CEP/extensions/SahAI";
}

// Global namespace for SahAI ExtendScript functions
var SahAI = SahAI || {};

/**
 * Initialize the ExtendScript environment
 */
SahAI.init = function() {
    try {
        // Set up error handling
        $.level = 1; // Enable debugging

        // Ensure extension directory exists
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            $.writeln("Creating extension directory: " + EXTENSION_DIR_PATH);
            // Try to create the directory, but don't fail if we can't
            try {
                if (!settingsDir.create()) {
                    $.writeln("Warning: Could not create extension directory: " + EXTENSION_DIR_PATH);
                } else {
                    $.writeln("Extension directory created successfully");
                }
            } catch (dirError) {
                $.writeln("Warning: Failed to create extension directory: " + dirError.toString());
            }
        } else {
            $.writeln("Extension directory already exists");
        }

        // Log initialization
        $.writeln("SahAI ExtendScript initialized successfully");

        return {
            success: true,
            message: "ExtendScript initialized",
            version: "2.0.0"
        };
    } catch (error) {
        $.writeln("Error initializing SahAI ExtendScript: " + error.toString());
        // Don't fail initialization completely, just log the error
        return {
            success: true, // Still return success so the extension can load
            message: "ExtendScript initialized with warnings: " + error.toString(),
            version: "2.0.0"
        };
    }
};

/**
 * Get application information
 */
SahAI.getAppInfo = function() {
    try {
        var result = {
            success: true,
            data: {
                name: app.name,
                version: app.version,
                locale: app.locale,
                build: app.build || "Unknown"
            }
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Execute code in the host application
 * @param {string} code - The code to execute
 * @param {string} language - The programming language (for context)
 */
SahAI.executeCode = function(code, language) {
    try {
        var result;
        
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'extendscript':
                // Execute ExtendScript code
                result = eval(code);
                break;
                
            case 'applescript':
                // Execute AppleScript (macOS only)
                if ($.os.indexOf("Mac") !== -1) {
                    result = app.doScript(code, ScriptLanguage.APPLESCRIPT_LANGUAGE);
                } else {
                    throw new Error("AppleScript is only supported on macOS");
                }
                break;
                
            case 'vbscript':
                // Execute VBScript (Windows only)
                if ($.os.indexOf("Win") !== -1) {
                    result = app.doScript(code, ScriptLanguage.VISUAL_BASIC);
                } else {
                    throw new Error("VBScript is only supported on Windows");
                }
                break;
                
            default:
                throw new Error("Unsupported language: " + language);
        }
        
        var successResult = {
            success: true,
            result: result ? result.toString() : "Code executed successfully",
            language: language
        };
        return JSON.stringify(successResult);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString(),
            language: language
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Get document information
 */
SahAI.getDocumentInfo = function() {
    try {
        if (!app.activeDocument) {
            var noDocResult = {
                success: false,
                message: "No active document"
            };
            return JSON.stringify(noDocResult);
        }

        var doc = app.activeDocument;
        var result = {
            success: true,
            data: {
                name: doc.name,
                path: doc.fullName ? doc.fullName.toString() : "Unsaved",
                saved: doc.saved,
                modified: doc.modified || false
            }
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Show alert dialog
 * @param {string} message - The message to display
 * @param {string} title - The dialog title
 */
SahAI.showAlert = function(message, title) {
    try {
        title = title || "SahAI";
        alert(message, title);
        var result = {
            success: true,
            message: "Alert displayed"
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Log message to ExtendScript console
 * @param {string} message - The message to log
 * @param {string} level - Log level (info, warn, error)
 */
SahAI.log = function(message, level) {
    try {
        level = level || "info";
        var timestamp = new Date().toISOString();
        var logMessage = "[" + timestamp + "] [" + level.toUpperCase() + "] " + message;

        $.writeln(logMessage);

        var result = {
            success: true,
            message: "Logged: " + message
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Get system information
 */
SahAI.getSystemInfo = function() {
    try {
        var result = {
            success: true,
            data: {
                os: $.os,
                version: $.version,
                buildDate: $.buildDate,
                locale: $.locale,
                memoryUsage: $.memCache
            }
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Load settings from file
 */
function loadSettings() {
    try {
        $.writeln("=== loadSettings called ===");

        // Ensure directory exists
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            $.writeln("Creating settings directory: " + EXTENSION_DIR_PATH);
            settingsDir.create();
        }

        var settingsFile = new File(SETTINGS_FILE_PATH);
        if (settingsFile.exists) {
            $.writeln("Loading settings from: " + SETTINGS_FILE_PATH);
            if (!settingsFile.open("r")) {
                throw new Error("Failed to open settings file for reading");
            }
            var content = settingsFile.read();
            settingsFile.close();

            if (content.trim() === '') {
                $.writeln("Settings file is empty, returning defaults");
                return JSON.stringify({ success: true, data: {} });
            }

            var parsedContent = JSON.parse(content);
            $.writeln("Settings loaded successfully");
            return JSON.stringify({ success: true, data: parsedContent });
        }
        $.writeln("No settings file found, returning defaults");
        return JSON.stringify({ success: true, data: {} });
    } catch (error) {
        $.writeln("=== loadSettings ERROR: " + error.toString() + " ===");
        return JSON.stringify({ success: false, message: error.toString(), data: {} });
    }
}

/**
 * Save settings to file
 * @param {Object} settings - Settings object to save
 */
function saveSettings(settingsString) {
    try {
        $.writeln("=== saveSettings called ===");

        // Parse the settings string if it's a string, otherwise use as-is
        var settings;
        if (typeof settingsString === 'string') {
            try {
                settings = JSON.parse(settingsString);
            } catch (parseError) {
                throw new Error("Invalid JSON string provided: " + parseError.toString());
            }
        } else {
            settings = settingsString;
        }

        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            $.writeln("Creating settings directory: " + EXTENSION_DIR_PATH);
            if (!settingsDir.create()) {
                throw new Error("Failed to create extension directory");
            }
        }

        var settingsFile = new File(SETTINGS_FILE_PATH);
        $.writeln("Saving settings to: " + SETTINGS_FILE_PATH);
        if (!settingsFile.open("w")) {
            throw new Error("Failed to open settings file for writing");
        }
        settingsFile.write(JSON.stringify(settings));
        settingsFile.close();

        $.writeln("Settings saved successfully");
        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("=== saveSettings ERROR: " + error.toString() + " ===");
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

/**
 * Load chat history from file
 */
function loadHistory() {
    try {
        var historyFile = new File(HISTORY_FILE_PATH);
        if (historyFile.exists) {
            historyFile.open("r");
            var content = historyFile.read();
            historyFile.close();
            return JSON.stringify({ success: true, data: JSON.parse(content) });
        }
        return JSON.stringify({ success: true, data: [] });
    } catch (error) {
        $.writeln("Error loading history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString(), data: [] });
    }
}

/**
 * Save chat history to file
 * @param {Array} history - Array of chat sessions to save
 */
function saveHistory(history) {
    try {
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var historyFile = new File(HISTORY_FILE_PATH);
        historyFile.open("w");
        historyFile.write(JSON.stringify(history));
        historyFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

// Model description and context length functions are now in modelData.jsx

/**
 * List models for different providers
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function listModels(providerId, baseURL, apiKey) {
    try {
        $.writeln("=== listModels called with: " + providerId + ", " + baseURL + ", " + (apiKey ? "[API_KEY_PROVIDED]" : "[NO_API_KEY]") + " ===");

        // Validate inputs
        if (!providerId) {
            throw new Error("Provider ID is required");
        }

        var responseBody;
        var models = [];

        function fetchWithRetry(fetchFn) {
            var maxRetries = 2;
            var currentRetry = 0;

            while (currentRetry <= maxRetries) {
                try {
                    return fetchFn();
                } catch (e) {
                    currentRetry++;
                    if (currentRetry > maxRetries) {
                        // Log the final failure
                        $.writeln('Failed after ' + maxRetries + ' retries: ' + e.toString());
                        throw new Error('Connection failed after ' + maxRetries + ' attempts: ' + e.toString());
                    }
                    // Log retry attempt
                    $.writeln('Retry attempt ' + currentRetry + '/' + maxRetries + ' after error: ' + e.toString());
                    $.sleep(1000); // Wait 1 second before retry
                }
            }
        }

        switch (providerId) {
            case 'ollama':
                // baseURL is like 'http://localhost:11434'
                var ollamaHost = baseURL.replace('http://', '').split(':')[0];
                var ollamaPort = parseInt(baseURL.replace('http://', '').split(':')[1] || '11434', 10);
                responseBody = fetchWithRetry(function() {
                    return makeRequest(ollamaHost, '/api/tags', 'GET', null, ollamaPort);
                });
                var ollamaData = JSON.parse(responseBody);
                // Handle "blob style" models with rich metadata
                if (ollamaData && ollamaData.models) {
                    for (var i = 0; i < ollamaData.models.length; i++) {
                        models.push({
                            id: ollamaData.models[i].name,
                            name: ollamaData.models[i].name,
                            description: 'Size: ' + (ollamaData.models[i].size / 1e9).toFixed(2) + ' GB',
                            contextLength: ollamaData.models[i].details ? ollamaData.models[i].details.parameter_size : 0
                        });
                    }
                }
                // If no models found, return empty array (don't auto-pull models without user consent)
                // Users can manually pull models through the UI if needed
                break;

            case 'openai':
            case 'groq': // Groq uses an OpenAI-compatible endpoint
                var host = (providerId === 'groq') ? 'api.groq.com' : 'api.openai.com';
                var path = (providerId === 'groq') ? '/openai/v1/models' : '/v1/models';
                responseBody = fetchWithRetry(function() {
                    return makeRequest(host, path, 'GET', apiKey);
                });
                var openAIData = JSON.parse(responseBody);
                if (openAIData && openAIData.data) {
                    for (var j = 0; j < openAIData.data.length; j++) {
                        models.push({
                            id: openAIData.data[j].id,
                            name: openAIData.data[j].id,
                            description: getModelDescription(providerId, openAIData.data[j].id),
                            contextLength: getModelContextLength(providerId, openAIData.data[j].id),
                            isRecommended: isModelRecommended(providerId, openAIData.data[j].id)
                        });
                    }
                }
                break;

            case 'anthropic':
                // Anthropic doesn't have a public models endpoint.
                // Return fallback models from shared data.
                models = getFallbackModels('anthropic');
                break;

            // Add cases for other providers like 'gemini', etc.
            case 'gemini':
                responseBody = fetchWithRetry(function() {
                    return makeRequest('generativelanguage.googleapis.com', '/v1beta/models?key=' + apiKey, 'GET');
                });
                var geminiData = JSON.parse(responseBody);
                if (geminiData && geminiData.models) {
                    for (var k = 0; k < geminiData.models.length; k++) {
                        models.push({
                            id: geminiData.models[k].name.replace('models/', ''),
                            name: geminiData.models[k].displayName || geminiData.models[k].name,
                            description: geminiData.models[k].description || 'Google Gemini model',
                            contextLength: geminiData.models[k].inputTokenLimit || 1000000,
                            isRecommended: geminiData.models[k].name.indexOf('gemini-1.5-pro') !== -1
                        });
                    }
                } else {
                    // Fallback models for Gemini
                    models = getFallbackModels('gemini');
                }
                break;

            case 'deepseek':
                // DeepSeek uses OpenAI-compatible API
                responseBody = fetchWithRetry(function() {
                    return makeRequest('api.deepseek.com', '/v1/models', 'GET', apiKey);
                });
                var deepseekData = JSON.parse(responseBody);
                if (deepseekData && deepseekData.data) {
                    for (var d = 0; d < deepseekData.data.length; d++) {
                        models.push({
                            id: deepseekData.data[d].id,
                            name: deepseekData.data[d].id,
                            description: getModelDescription('deepseek', deepseekData.data[d].id),
                            contextLength: getModelContextLength('deepseek', deepseekData.data[d].id),
                            isRecommended: isModelRecommended('deepseek', deepseekData.data[d].id)
                        });
                    }
                } else {
                    // Fallback models for DeepSeek
                    models = getFallbackModels('deepseek');
                }
                break;

            case 'openrouter':
                // OpenRouter uses OpenAI-compatible API
                responseBody = fetchWithRetry(function() {
                    return makeRequest('openrouter.ai', '/api/v1/models', 'GET', apiKey);
                });
                var openrouterData = JSON.parse(responseBody);
                if (openrouterData && openrouterData.data) {
                    for (var o = 0; o < openrouterData.data.length; o++) {
                        models.push({
                            id: openrouterData.data[o].id,
                            name: openrouterData.data[o].name || openrouterData.data[o].id,
                            description: openrouterData.data[o].description || getModelDescription('openrouter', openrouterData.data[o].id),
                            contextLength: openrouterData.data[o].context_length || getModelContextLength('openrouter', openrouterData.data[o].id),
                            isRecommended: isModelRecommended('openrouter', openrouterData.data[o].id)
                        });
                    }
                } else {
                    // Fallback models for OpenRouter
                    models = getFallbackModels('openrouter');
                }
                break;

            default:
                throw new Error("Unsupported provider: " + providerId);
        }

        $.writeln("=== listModels returning " + models.length + " models for " + providerId + " ===");
        return JSON.stringify({ success: true, data: models });

    } catch (e) {
        // Enhanced error handling with more specific error messages
        var errorMessage = e.toString();
        var errorType = 'UNKNOWN_ERROR';

        $.writeln("=== listModels ERROR for " + providerId + ": " + errorMessage + " ===");

        // Categorize common errors
        if (errorMessage.indexOf('Failed to connect') !== -1 || errorMessage.indexOf('connection') !== -1 || errorMessage.indexOf('ECONNREFUSED') !== -1) {
            errorType = 'CONNECTION_ERROR';
            errorMessage = 'Cannot connect to ' + providerId + '. Please check if the service is running and accessible.';
        } else if (errorMessage.indexOf('timeout') !== -1 || errorMessage.indexOf('timed out') !== -1) {
            errorType = 'TIMEOUT_ERROR';
            errorMessage = 'Request timed out. The service may be slow or unavailable.';
        } else if (errorMessage.indexOf('JSON') !== -1 || errorMessage.indexOf('parse') !== -1) {
            errorType = 'PARSE_ERROR';
            errorMessage = 'Invalid response from ' + providerId + '. The service may be misconfigured.';
        } else if (errorMessage.indexOf('Unsupported provider') !== -1) {
            errorType = 'UNSUPPORTED_PROVIDER';
            errorMessage = 'Provider "' + providerId + '" is not supported.';
        } else if (errorMessage.indexOf('401') !== -1 || errorMessage.indexOf('Unauthorized') !== -1) {
            errorType = 'AUTH_ERROR';
            errorMessage = 'Invalid API key for ' + providerId + '. Please check your credentials.';
        } else if (errorMessage.indexOf('403') !== -1 || errorMessage.indexOf('Forbidden') !== -1) {
            errorType = 'AUTH_ERROR';
            errorMessage = 'Access denied for ' + providerId + '. Please check your API key permissions.';
        } else if (errorMessage.indexOf('429') !== -1 || errorMessage.indexOf('rate limit') !== -1) {
            errorType = 'RATE_LIMIT_ERROR';
            errorMessage = 'Rate limit exceeded for ' + providerId + '. Please try again later.';
        }

        $.writeln('Error in loadModelsForProvider(' + providerId + '): ' + errorMessage);

        return JSON.stringify({
            success: false,
            message: errorMessage,
            data: []
        });
    }
}

// getFallbackModels function is now in modelData.jsx

/**
 * Makes an HTTP request using ExtendScript's Socket object.
 * @param {string} host The domain name (e.g., 'api.openai.com').
 * @param {string} path The API path (e.g., '/v1/models').
 * @param {string} method The HTTP method ('GET', 'POST', etc.).
 * @param {string} [apiKey] The Bearer token API key.
 * @param {number} [port=443] The port number (443 for HTTPS).
 * @param {string} [body] The request body for POST requests.
 * @returns {string} The raw JSON response string.
 */
function makeRequest(host, path, method, apiKey, port, body) {
    port = port || HTTPS_DEFAULT_PORT;
    var conn = new Socket();
    var responseBody = '';

    if (conn.open(host + ':' + port, 'UTF-8', undefined, true)) {
        var headers = [
            method + ' ' + path + ' HTTP/1.1',
            'Host: ' + host,
            'Content-Type: application/json',
            'User-Agent: SahAI-CEP-Extension/2.0',
            'Accept: application/json',
            'Cache-Control: no-cache'
        ];

        if (apiKey) {
            headers.push('Authorization: Bearer ' + apiKey);
        }

        // Add content length for POST requests
        if (body && method === 'POST') {
            headers.push('Content-Length: ' + body.length);
        }

        // Send headers
        conn.write(headers.join('\r\n') + '\r\n\r\n');

        // Send body if present
        if (body && method === 'POST') {
            conn.write(body);
        }

        var response = conn.read();
        conn.close();

        // Extract the JSON body from the HTTP response
        var bodyStartIndex = response.indexOf('\r\n\r\n');
        if (bodyStartIndex > -1) {
            responseBody = response.substring(bodyStartIndex + 4);
        } else {
            // Fallback for responses without headers (e.g., some Ollama setups)
            bodyStartIndex = response.indexOf('{');
            var bodyEndIndex = response.lastIndexOf('}');
            if(bodyStartIndex > -1 && bodyEndIndex > -1) {
               responseBody = response.substring(bodyStartIndex, bodyEndIndex + 1);
            }
        }
        return responseBody;
    }
    throw new Error('Failed to connect to ' + host);
}

/**
 * Helper function to make HTTP requests with timeout and better error handling
 * @param {string} url - URL to fetch
 * @param {Object} headers - Optional headers
 * @param {number} timeout - Timeout in milliseconds (default: 10000)
 * @param {number} retries - Number of retry attempts (default: 2)
 */
function getURL(url, headers, timeout, retries) {
    timeout = timeout || 10000; // Default 10 second timeout (reduced for better UX)
    retries = retries || 2; // Default 2 retries

    var lastError = null;

    for (var attempt = 0; attempt <= retries; attempt++) {
        try {
            $.writeln("Making HTTP request to: " + url + " (attempt " + (attempt + 1) + "/" + (retries + 1) + ")");

            var http = new XMLHttpRequest();
            var startTime = new Date().getTime();

            // Enhanced timeout handling for ExtendScript
            var timeoutId = null;
            var timedOut = false;

            // Manual timeout implementation since XMLHttpRequest.timeout may not work
            if (timeout > 0) {
                timeoutId = setTimeout(function() {
                    timedOut = true;
                    try {
                        http.abort();
                    } catch (e) {
                        // Ignore abort errors
                    }
                }, timeout);
            }

            http.open("GET", url, false); // Synchronous request

            // Set default headers with better compatibility
            try {
                http.setRequestHeader("User-Agent", "SahAI-CEP-Extension/2.0");
                http.setRequestHeader("Accept", "application/json");
                http.setRequestHeader("Cache-Control", "no-cache");

                // Add CORS headers for local providers
                if (url.indexOf('localhost') !== -1 || url.indexOf('127.0.0.1') !== -1) {
                    http.setRequestHeader("Access-Control-Allow-Origin", "*");
                }
            } catch (headerError) {
                $.writeln("Warning: Could not set some headers: " + headerError.toString());
            }

            // Set custom headers
            if (headers) {
                for (var key in headers) {
                    if (headers.hasOwnProperty(key)) {
                        try {
                            http.setRequestHeader(key, headers[key]);
                        } catch (headerError) {
                            $.writeln("Warning: Could not set header " + key + ": " + headerError.toString());
                        }
                    }
                }
            }

            // Send request
            http.send();

            // Clear timeout
            if (timeoutId) {
                clearTimeout(timeoutId);
            }

            // Check if request timed out
            if (timedOut) {
                throw new Error("Request timed out after " + timeout + "ms");
            }

            var endTime = new Date().getTime();
            var duration = endTime - startTime;
            $.writeln("HTTP response status: " + http.status + " (took " + duration + "ms)");

            if (http.status >= 200 && http.status < 300) {
                return { success: true, data: http.responseText, duration: duration };
            } else if (http.status === 401) {
                return { success: false, message: "Unauthorized - Invalid API key" };
            } else if (http.status === 403) {
                return { success: false, message: "Forbidden - Access denied" };
            } else if (http.status === 404) {
                return { success: false, message: "Not Found - API endpoint not found" };
            } else if (http.status === 429) {
                return { success: false, message: "Rate Limited - Too many requests" };
            } else if (http.status >= 500) {
                return { success: false, message: "Server Error - Please try again later" };
            } else {
                throw new Error("HTTP " + http.status + " - " + http.statusText);
            }
        } catch (error) {
            lastError = error;
            var errorMessage = error.toString();
            $.writeln("HTTP request error (attempt " + (attempt + 1) + "): " + errorMessage);

            // Don't retry on certain errors
            if (errorMessage.indexOf('401') !== -1 || errorMessage.indexOf('403') !== -1 ||
                errorMessage.indexOf('Unauthorized') !== -1 || errorMessage.indexOf('Forbidden') !== -1) {
                break; // Don't retry auth errors
            }

      // Wait before retry (exponential backoff)
      if (attempt < retries) {
        var delay = Math.min(1000 * Math.pow(2, attempt), 3000); // Max 3s delay
        $.writeln("Retrying in " + delay + "ms...");

        // Use a more efficient delay mechanism
        var startDelay = new Date().getTime();
        var currentDelay = 0;
        while (currentDelay < delay) {
          // Small sleep to prevent blocking the thread
          $.sleep(10);
          currentDelay = new Date().getTime() - startDelay;
        }
      }
        }
    }

    // All attempts failed
    var errorMessage = lastError ? lastError.toString() : "Unknown error";
    if (errorMessage.indexOf('timeout') !== -1 || errorMessage.indexOf('timed out') !== -1) {
        return { success: false, message: "Request timed out after " + timeout + "ms" };
    } else if (errorMessage.indexOf('network') !== -1 || errorMessage.indexOf('connection') !== -1) {
        return { success: false, message: "Network connection error" };
    } else {
        return { success: false, message: errorMessage };
    }
}

/**
 * Fetch models for a specific provider (wrapper for listModels)
 * This function is called by the ProviderBridge in cepIntegration.ts
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function fetchModels(providerId, baseURL, apiKey) {
    try {
        $.writeln("fetchModels called with providerId: " + providerId + ", baseURL: " + (baseURL || 'default') + ", apiKey: " + (apiKey ? 'provided' : 'not provided'));

        // Call the existing listModels function
        var result = listModels(providerId, baseURL, apiKey);
        var parsedResult = JSON.parse(result);

        // Transform the result to match expected format
        if (parsedResult.ok) {
            return JSON.stringify(parsedResult.models);
        } else {
            return JSON.stringify({ error: parsedResult.error || 'Failed to fetch models' });
        }
    } catch (error) {
        $.writeln("Error in fetchModels: " + error.toString());
        return JSON.stringify({ error: error.toString() });
    }
}

// Initialize SahAI when script loads
try {
    $.writeln("=== SahAI Extension Loading ===");
    SahAI.init();
    $.writeln("=== SahAI Extension Loaded Successfully ===");
} catch (error) {
    $.writeln("=== SahAI Extension Load Error: " + error.toString() + " ===");
}
