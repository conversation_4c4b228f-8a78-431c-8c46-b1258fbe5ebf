const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./modelData-C4Dl7wzs.js","./vendor-DsceW-4w.js","./ui-B3AhFxSy.js","./shiki-Dr4f9TeX.js"])))=>i.map(i=>d[i]);
var Ne=Object.defineProperty;var Se=(r,a,t)=>a in r?Ne(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t;var L=(r,a,t)=>Se(r,typeof a!="symbol"?a+"":a,t);import"../CSInterface.js";import{r as b,a as we,R as le}from"./vendor-DsceW-4w.js";import{c as $,L as Z,C as O,P as Ee,H as Ce,S as ke,a as Re,T as Te,D as Ie,b as Le,M as W,A as Pe,d as Ae,e as Oe,f as Me,g as ue,h as me,R as ee,X as M,i as oe,I as Y,B as De,j as _e,k as Ue,l as $e,m as Q,W as Fe,n as ze,o as Ke}from"./ui-B3AhFxSy.js";import{_ as He,c as Be}from"./shiki-Dr4f9TeX.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const n of o)if(n.type==="childList")for(const i of n.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function t(o){const n={};return o.integrity&&(n.integrity=o.integrity),o.referrerPolicy&&(n.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?n.credentials="include":o.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function s(o){if(o.ep)return;o.ep=!0;const n=t(o);fetch(o.href,n)}})();var xe={exports:{}},B={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qe=b,Ge=Symbol.for("react.element"),Je=Symbol.for("react.fragment"),Ve=Object.prototype.hasOwnProperty,We=qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ye={key:!0,ref:!0,__self:!0,__source:!0};function be(r,a,t){var s,o={},n=null,i=null;t!==void 0&&(n=""+t),a.key!==void 0&&(n=""+a.key),a.ref!==void 0&&(i=a.ref);for(s in a)Ve.call(a,s)&&!Ye.hasOwnProperty(s)&&(o[s]=a[s]);if(r&&r.defaultProps)for(s in a=r.defaultProps,a)o[s]===void 0&&(o[s]=a[s]);return{$$typeof:Ge,type:r,key:n,ref:i,props:o,_owner:We.current}}B.Fragment=Je;B.jsx=be;B.jsxs=be;xe.exports=B;var e=xe.exports,X={},ne=we;X.createRoot=ne.createRoot,X.hydrateRoot=ne.hydrateRoot;const Qe=r=>(a,t,s)=>{const o=s.subscribe;return s.subscribe=(i,c,l)=>{let u=i;if(c){const m=l?.equalityFn||Object.is;let p=i(s.getState());u=g=>{const f=i(g);if(!m(p,f)){const h=p;c(p=f,h)}},l?.fireImmediately&&c(p,p)}return o(u)},r(a,t,s)},Xe=Qe,Ze=15e3,et=1,G=8e3,z=1e3,tt=4e3,ie=3e3,ce=5e3,Kt=4096,Ht=8192,Bt=32e3,qt=128e3,Gt=2e5,st="sahAI_settings",C="sahai-chat-history";let J=null,te=0,se=0;const rt=5,at=3e4,de=()=>te<rt?!1:Date.now()-se<at,ot=()=>{te++,se=Date.now()},nt=()=>{te=0,se=0},U=()=>typeof window<"u"&&!!window.CSInterface,K=()=>{if(!J&&U())try{J=new window.CSInterface,console.log("CSInterface initialized successfully")}catch(r){console.error("Failed to initialize CSInterface:",r)}return J},it=()=>{if(!U()){console.warn("Not running in CEP environment");return}const r=K();if(!r)return;r.addEventListener("com.adobe.csxs.events.ThemeColorChanged",t=>{console.log("Theme changed:",t)});const a=r.getHostEnvironment();console.log("Host environment:",a),r.evalScript("SahAI.getAppInfo()",t=>{try{if(!t||t.trim()===""){console.warn("Empty response from ExtendScript");return}const s=JSON.parse(t);console.log("ExtendScript response:",s)}catch(s){console.error("Failed to parse ExtendScript response:",s,"Raw result:",t)}})},ct=r=>r.startsWith("EvalScript error")||r.trim()===""&&!r.includes("undefined")||r.includes("timeout"),dt=r=>{try{return JSON.parse(r)}catch{return{success:!0,data:r}}},lt=r=>typeof r=="object"&&r!==null&&r.success===!1&&(r.message?.includes("timeout")||r.message?.includes("connection"))||!1,T=(r,a=Ze,t=et)=>new Promise((s,o)=>{const n=K();if(!n){o(new Error("CSInterface not available - not running in CEP environment"));return}let i=0;const c=()=>{i++;const l=setTimeout(()=>{i<=t?(console.warn(`ExtendScript execution attempt ${i} timed out, retrying...`),c()):o(new Error(`ExtendScript execution timed out after ${a}ms (${t+1} attempts)`))},a);try{n.evalScript(r,u=>{clearTimeout(l);try{if(ct(u)){if(i<=t){console.warn(`Retryable error on attempt ${i}, retrying...`),setTimeout(c,z);return}o(new Error(u.startsWith("EvalScript error")?`ExtendScript Error: ${u}`:"Empty response from ExtendScript after all retries"));return}const m=dt(u);if(lt(m)){if(i<=t){console.warn(`ExtendScript returned failure on attempt ${i}, retrying...`),setTimeout(c,z);return}o(new Error(m.message||"ExtendScript execution failed"));return}s(typeof m=="object"&&m!==null?m:{success:!0,data:m})}catch(m){if(i<=t){console.warn(`Error processing response on attempt ${i}, retrying...`),setTimeout(c,z);return}o(new Error(`Failed to process ExtendScript response: ${m}`))}})}catch(u){if(clearTimeout(l),i<=t){console.warn(`Error executing ExtendScript on attempt ${i}, retrying...`),setTimeout(c,z);return}o(new Error(`Failed to execute ExtendScript: ${u}`))}};c()});class A{static async save(a){const t=JSON.stringify(a);try{if(U()&&!de())try{const s=await T(`saveSettings('${JSON.stringify(a).replace(/'/g,"\\'")}')`,G);if(s.success)console.log("Settings saved to CEP storage successfully"),nt();else throw new Error(s.message||"CEP save failed")}catch(s){ot(),console.warn("CEP storage save failed, falling back to localStorage:",s)}else de()&&console.warn("CEP circuit breaker is open, skipping CEP save");localStorage.setItem(this.SETTINGS_KEY,t),console.log("Settings saved to localStorage successfully")}catch(s){console.error("All settings save methods failed:",s);try{localStorage.setItem(this.SETTINGS_KEY,t)}catch(o){throw new Error(`Failed to save settings: ${s}. LocalStorage also failed: ${o}`)}}}static async load(){try{const a=[{name:"CEP Storage",load:async()=>{if(U()){const t=await T("loadSettings()",G);if(t.success)return t.data&&Object.keys(t.data).length>0?t.data:null}return null}},{name:"LocalStorage",load:async()=>{const t=localStorage.getItem(this.SETTINGS_KEY);if(t)try{const s=JSON.parse(t);if(s&&Object.keys(s).length>0)return s}catch(s){console.warn("Failed to parse localStorage settings:",s)}return null}}];for(const t of a)try{const s=await t.load();if(s)return console.log(`Settings loaded from ${t.name} successfully`),s}catch(s){console.warn(`Failed to load settings from ${t.name}:`,s)}return console.log("No existing settings found in any storage, returning defaults"),{providers:[]}}catch(a){return console.error("All settings load methods failed:",a),{providers:[]}}}static async exportSettings(){const a=await this.load();return JSON.stringify(a,null,2)}static async importSettings(a){try{const t=JSON.parse(a);await this.save(t)}catch{throw new Error("Invalid settings format")}}static async clearSettings(){try{if(U())try{await T("saveSettings('{}')",G)}catch(a){console.warn("Failed to clear CEP storage:",a)}localStorage.removeItem(this.SETTINGS_KEY),console.log("Settings cleared successfully")}catch(a){throw new Error(`Failed to clear settings: ${a}`)}}}L(A,"SETTINGS_KEY",st);class ge{static async checkProviderStatus(a,t){const s=Date.now();try{return{isOnline:(await he.listModels(a,t.baseURL,t.apiKey)).length>0,latency:Date.now()-s}}catch(o){return{isOnline:!1,error:o.message||String(o),latency:Date.now()-s}}}}Promise.prototype.timeout||(Promise.prototype.timeout=function(r){return Promise.race([this,new Promise((a,t)=>setTimeout(()=>t(new Error(`Operation timed out after ${r}ms`)),r))])});const he={async listModels(r,a,t){return new Promise((s,o)=>{const n=K();if(!n){o(new Error("CSInterface not available - not running in CEP environment"));return}const i=`listModels("${r}", "${a||""}", "${t||""}")`;n.evalScript(i,c=>{try{if(!c||c.trim()===""||c.trim()==="undefined")return o(new Error("No response from ExtendScript. Ensure the extension is properly loaded and the service is accessible."));if(c.startsWith("EvalScript error")){const u=c.replace("EvalScript error - ","");return o(new Error(`Service Error: ${u}`))}let l;try{l=JSON.parse(c)}catch{return o(new Error(`Invalid response from service: ${c.substring(0,100)}...`))}if(l.success&&Array.isArray(l.data)){const u=l.data.map(m=>({id:String(m.id||"unknown-id"),name:String(m.name||m.id||"Unknown Model"),description:String(m.description||""),contextLength:Number(m.contextLength||m.context_length||4096),isRecommended:!!(m.isRecommended||m.is_recommended)}));s(u)}else{const u=l.message||"Failed to load models from service";o(new Error(u))}}catch(l){o(new Error(`Failed to process response: ${l.message}`))}})})},async pullModel(r,a,t){if(r!=="ollama")throw new Error("Pull only supported for Ollama");return new Promise((s,o)=>{const n=K();if(!n){o(new Error("CSInterface not available - not running in CEP environment"));return}const i=t.replace("http://","").split(":")[0],c=parseInt(t.split(":")[2]||"11434",10),l=`
        try {
          var result = makeRequest('${i}', '/api/pull', 'POST', null, ${c}, JSON.stringify({name: '${a}'}));
          JSON.stringify({success: true, message: 'Model pull initiated'});
        } catch (e) {
          JSON.stringify({success: false, message: e.toString()});
        }
      `;n.evalScript(l,u=>{try{const m=JSON.parse(u);m.success?s(m):o(new Error(m.message))}catch(m){o(new Error(`Failed to parse pull response: ${m}`))}})})},async getFallbackModels(r){const{getFallbackModels:a}=await He(async()=>{const{getFallbackModels:t}=await import("./modelData-C4Dl7wzs.js");return{getFallbackModels:t}},__vite__mapDeps([0,1,2,3]),import.meta.url);return a(r)}},I=$((r,a)=>({toasts:[],addToast:t=>{const s=`toast-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,o={...t,id:s,isVisible:!0,duration:t.duration||tt};r(n=>({toasts:[...n.toasts,o]})),setTimeout(()=>{a().removeToast(s)},o.duration)},removeToast:t=>{r(s=>({toasts:s.toasts.filter(o=>o.id!==t)}))},clearAllToasts:()=>{r({toasts:[]})}})),k={success:(r,a,t)=>{I.getState().addToast({type:"success",title:r,message:a,duration:t})},error:(r,a,t)=>{I.getState().addToast({type:"error",title:r,message:a,duration:t})},warning:(r,a,t)=>{I.getState().addToast({type:"warning",title:r,message:a,duration:t})},info:(r,a,t)=>{I.getState().addToast({type:"info",title:r,message:a,duration:t})}},ut=[{pattern:/timeout|timed out/i,code:"TIMEOUT_ERROR",userMessage:"Request timed out. Please check your internet connection and try again.",severity:"medium",retryable:!0},{pattern:/network|connection|ECONNREFUSED|ENOTFOUND/i,code:"NETWORK_ERROR",userMessage:"Network error. Please check your internet connection.",severity:"medium",retryable:!0},{pattern:/failed to connect|connection failed/i,code:"CONNECTION_FAILED",userMessage:"Failed to connect to the service. Please try again later.",severity:"medium",retryable:!0},{pattern:/unauthorized|401/i,code:"UNAUTHORIZED",userMessage:"Invalid API key. Please check your credentials.",severity:"high",retryable:!1},{pattern:/forbidden|403/i,code:"FORBIDDEN",userMessage:"Access denied. Please check your API key permissions.",severity:"high",retryable:!1},{pattern:/invalid.*api.*key|api.*key.*invalid/i,code:"INVALID_API_KEY",userMessage:"Invalid API key format. Please check your API key.",severity:"high",retryable:!1},{pattern:/not found|404/i,code:"NOT_FOUND",userMessage:"API endpoint not found. Please check the provider configuration.",severity:"medium",retryable:!1},{pattern:/rate limit|429|too many requests/i,code:"RATE_LIMITED",userMessage:"Rate limit exceeded. Please wait a moment and try again.",severity:"medium",retryable:!0},{pattern:/server error|500|502|503|504/i,code:"SERVER_ERROR",userMessage:"Server error. Please try again later.",severity:"medium",retryable:!0},{pattern:/bad request|400/i,code:"BAD_REQUEST",userMessage:"Invalid request. Please check your configuration.",severity:"medium",retryable:!1},{pattern:/CSInterface not available|not running in CEP/i,code:"CEP_NOT_AVAILABLE",userMessage:"CEP environment not available. Please run this in Adobe application.",severity:"high",retryable:!1},{pattern:/ExtendScript|EvalScript error/i,code:"EXTENDSCRIPT_ERROR",userMessage:"Script execution error. Please try again.",severity:"medium",retryable:!0},{pattern:/API Key required|Base URL required/i,code:"MISSING_CONFIG",userMessage:"Configuration missing. Please provide required credentials.",severity:"medium",retryable:!1},{pattern:/invalid.*config|config.*invalid/i,code:"INVALID_CONFIG",userMessage:"Invalid configuration. Please check your settings.",severity:"medium",retryable:!1},{pattern:/ollama.*not.*accessible|cannot connect to ollama/i,code:"CONNECTION_FAILED",userMessage:"Cannot connect to Ollama service. Ensure Ollama is running.",severity:"medium",retryable:!0},{pattern:/ollama.*service.*error|ollama.*error/i,code:"SERVER_ERROR",userMessage:"Ollama service error. Ensure Ollama is running and accessible.",severity:"medium",retryable:!0}];function pe(r){let a=r?.message||String(r);if(r&&typeof r=="object"){if(r.message&&(a=r.message),a.includes("timeout")||a.includes("timed out"))return{code:"TIMEOUT_ERROR",message:a,userMessage:"Request timed out. Please check your connection and try again.",severity:"medium",retryable:!0};if(a.includes("ENOTFOUND")||a.includes("ECONNREFUSED")||a.includes("Failed to connect"))return{code:"NETWORK_ERROR",message:a,userMessage:"Cannot connect to the service. Please check if it's running and accessible.",severity:"medium",retryable:!0};if(a.includes("401")||a.includes("Unauthorized")||a.includes("Invalid API key"))return{code:"UNAUTHORIZED",message:a,userMessage:"Invalid API key. Please check your credentials and try again.",severity:"high",retryable:!1};if(a.includes("403"))return{code:"FORBIDDEN",message:a,userMessage:"Access denied. Please check your API permissions.",severity:"high",retryable:!1};if(a.includes("429"))return{code:"RATE_LIMITED",message:a,userMessage:"Rate limit exceeded. Please try again later.",severity:"medium",retryable:!0};if(a.includes("JSON"))return{code:"PARSING_ERROR",message:a,userMessage:"Invalid response from service. Please check service status.",severity:"medium",retryable:!0};if(a.includes("Ollama"))return{code:"CONNECTION_FAILED",message:a,userMessage:"Ollama service error. Ensure Ollama is running and accessible.",severity:"medium",retryable:!0};if(a.includes("Service Error")){const t=a.replace("Service Error: ","");return{code:"SERVER_ERROR",message:a,userMessage:t||"Service error occurred. Please try again.",severity:"medium",retryable:!0}}}else if(typeof r=="string"){if(a=r,r.includes("timeout"))return{code:"TIMEOUT_ERROR",message:a,userMessage:"Request timed out. Please check your connection and try again.",severity:"medium",retryable:!0};if(r.includes("Unauthorized")||r.includes("Invalid API key"))return{code:"UNAUTHORIZED",message:a,userMessage:"Invalid API key. Please check your credentials and try again.",severity:"high",retryable:!1};if(r.includes("Ollama"))return{code:"CONNECTION_FAILED",message:a,userMessage:"Ollama service error. Ensure Ollama is running and accessible.",severity:"medium",retryable:!0};if(r.includes("Service Error")){const t=r.replace("Service Error: ","");return{code:"SERVER_ERROR",message:a,userMessage:t||"Service error occurred. Please try again.",severity:"medium",retryable:!0}}}for(const t of ut)if(t.pattern instanceof RegExp?t.pattern.test(a):a.includes(t.pattern))return{code:t.code,message:a,userMessage:t.userMessage,severity:t.severity,retryable:t.retryable};return{code:"UNKNOWN_ERROR",message:a,userMessage:"An unexpected error occurred. Please try again.",severity:"medium",retryable:!0}}function mt(r,a){const t=pe(r);return`${a?`[${a}] `:""}${t.code}: ${t.message}`}const xt=r=>r.configType==="baseURL"&&!r.baseURL?.trim()?"Base URL required":r.configType==="apiKey"&&!r.apiKey?.trim()?"API Key required":null,bt=(r,a)=>Array.isArray(r)?r.map(t=>({id:t.id||"unknown-id",name:t.name||t.id||"Unknown Model",description:t.description||"",contextLength:t.contextLength||t.context_length||4096,isRecommended:t.isRecommended||t.is_recommended||!1})):(console.warn("Invalid models data received:",r),[]),D=$()(Xe((r,a)=>({providers:[{id:"openai",name:"OpenAI",configType:"apiKey",isConfigured:!1,models:[]},{id:"anthropic",name:"Anthropic",configType:"apiKey",isConfigured:!1,models:[]},{id:"gemini",name:"Google Gemini",configType:"apiKey",isConfigured:!1,models:[]},{id:"groq",name:"Groq",configType:"apiKey",isConfigured:!1,models:[]},{id:"deepseek",name:"DeepSeek",configType:"apiKey",isConfigured:!1,models:[]},{id:"openrouter",name:"OpenRouter",configType:"apiKey",isConfigured:!1,models:[]},{id:"ollama",name:"Ollama",configType:"baseURL",isConfigured:!1,models:[]}],activeProviderId:"openai",isLoadingModels:!1,setActiveProvider:t=>{r(o=>({activeProviderId:t,providers:o.providers.map(n=>n.id!==t?{...n,models:[],isLoading:!1,error:void 0}:n)})),a().persistSettings();const s=a().providers.find(o=>o.id===t);s&&s.isConfigured&&(s.apiKey||s.baseURL)&&t!=="ollama"&&a().loadModelsForProvider(t)},updateProviderConfig:(t,s)=>{r(o=>({providers:o.providers.map(n=>n.id===t?{...n,...s,isConfigured:!!(s.apiKey?.trim()||s.baseURL?.trim())}:n)})),a().persistSettings(),(s.apiKey?.trim()||s.baseURL?.trim())&&t!=="ollama"&&a().loadModelsForProvider(t)},setProviderModels:(t,s)=>{r(o=>({providers:o.providers.map(n=>n.id===t?{...n,models:s,isLoading:!1,error:void 0}:n)}))},setSelectedModel:(t,s)=>{r(o=>({providers:o.providers.map(n=>n.id===t?{...n,selectedModelId:s}:n)})),a().persistSettings()},updateProviderKey:(t,s,o)=>{r(n=>({providers:n.providers.map(i=>i.id===t?{...i,apiKey:s,isConfigured:!!s?.trim(),selectedModelId:o||i.selectedModelId}:i)})),a().persistSettings(),s?.trim()&&t!=="ollama"&&a().loadModelsForProvider(t)},saveProviderSelection:(t,s)=>{const o=a().providers.find(n=>n.id===t);r(n=>({activeProviderId:t,providers:n.providers.map(i=>i.id===t?{...i,...s,isConfigured:!!(s.apiKey?.trim()||s.baseURL?.trim())}:i)})),a().persistSettings(),k.success("Provider configured",`${o?.name||t} has been configured successfully`,ie),(s.apiKey?.trim()||s.baseURL?.trim())&&t!=="ollama"&&a().loadModelsForProvider(t)},loadModelsForProvider:async t=>{const s=a().providers.find(n=>n.id===t);if(!s)return;if(s.isLoading){console.log(`Already loading models for ${t}, skipping...`);return}if(t==="ollama"){const n=s.baseURL||"http://localhost:11434";try{const i=`${n.replace(/\/$/,"")}/api/tags`,c=new AbortController,l=setTimeout(()=>c.abort(),5e3),u=await fetch(i,{method:"GET",signal:c.signal});if(clearTimeout(l),!u.ok)throw new Error(`Ollama service not accessible at ${n}`)}catch(i){const c=i.name==="AbortError"?"Ollama connection timed out. Ensure it is running.":"Cannot connect to Ollama service. Ensure it is running.";r(l=>({providers:l.providers.map(u=>u.id===t?{...u,isLoading:!1,error:c}:u)})),k.error("Ollama Connection Failed",c,ce);return}}const o=xt(s);if(o){r(n=>({providers:n.providers.map(i=>i.id===t?{...i,error:o}:i)}));return}r(n=>({providers:n.providers.map(i=>i.id===t?{...i,isLoading:!0,error:void 0}:i)}));try{const n=await he.listModels(t,s.baseURL,s.apiKey);if(!Array.isArray(n))throw new Error("Invalid response format from provider");const i=bt(n,t);r(c=>({providers:c.providers.map(l=>l.id===t?{...l,models:i,isLoading:!1,error:void 0}:l)})),i.length>0&&!s.selectedModelId&&r(c=>({providers:c.providers.map(l=>l.id===t?{...l,selectedModelId:i[0].id}:l)})),k.success("Models loaded successfully",`Found ${i.length} models for ${s.name}`,ie)}catch(n){const i=pe(n);console.error(mt(n,`loadModelsForProvider:${t}`)),r(l=>({providers:l.providers.map(u=>u.id===t?{...u,isLoading:!1,error:i.userMessage}:u)}));const c=s?.name||t;k.error(`Failed to load ${c} models`,i.userMessage,ce)}},persistSettings:()=>{const{activeProviderId:t,providers:s}=a();A.save({activeProviderId:t,providers:s.map(o=>({id:o.id,isConfigured:o.isConfigured,configType:o.configType,apiKey:o.apiKey,baseURL:o.baseURL,selectedModelId:o.selectedModelId,settings:o.settings}))})},loadSettings:async()=>{try{const t=await A.load(),s=t.activeProviderId||"openai";r({activeProviderId:s}),t.providers&&Array.isArray(t.providers)&&r(i=>({providers:i.providers.map(c=>{const l=t.providers?.find(u=>u.id===c.id);return l?{...c,...l}:c})}));const o=a(),n=o.providers.find(i=>i.id===o.activeProviderId);n&&n.isConfigured&&(n.apiKey||n.baseURL)&&n.id!=="ollama"&&setTimeout(()=>{a().loadModelsForProvider(n.id)},0)}catch(t){console.error("Failed to load CEP settings:",t),r({activeProviderId:"openai"})}},getActiveProvider:()=>{const{providers:t,activeProviderId:s}=a();return t.find(o=>o.id===s)||null},getActiveModel:()=>{const t=a().getActiveProvider();return t?.selectedModelId&&t.models.find(s=>s.id===t.selectedModelId)||null}}))),_=$(r=>({modal:null,openModal:a=>r({modal:a}),closeModal:()=>r({modal:null})})),H=$((r,a)=>({sessions:[],currentSessionId:null,isLoading:!1,error:null,loadHistory:async()=>{r({isLoading:!0,error:null});try{if(!window.CSInterface){const o=localStorage.getItem(C),n=o?JSON.parse(o):[];r({sessions:n,isLoading:!1});return}const s=await T("loadHistory()");if(s&&s.success&&s.data){const o=Array.isArray(s.data)?s.data:[];r({sessions:o,isLoading:!1})}else if(s&&s.success)r({sessions:[],isLoading:!1});else throw new Error(s?.message||"Failed to load history from ExtendScript")}catch(t){console.error("Failed to load history:",t);try{const s=localStorage.getItem(C),o=s?JSON.parse(s):[];r({sessions:o,isLoading:!1,error:`Using local storage fallback: ${t.message}`})}catch{r({error:t.message||"Failed to load chat history",isLoading:!1,sessions:[]})}}},saveSession:async t=>{try{r(n=>({sessions:n.sessions.some(i=>i.id===t.id)?n.sessions.map(i=>i.id===t.id?t:i):[...n.sessions,t]}));const s=a().sessions;if(!window.CSInterface){localStorage.setItem(C,JSON.stringify(s));return}await T(`saveHistory(${JSON.stringify(s)})`),localStorage.setItem(C,JSON.stringify(s))}catch(s){console.error("Failed to save session:",s);try{const o=a().sessions;localStorage.setItem(C,JSON.stringify(o))}catch{r({error:s.message||"Failed to save session"})}}},deleteSession:async t=>{try{r(n=>({sessions:n.sessions.filter(i=>i.id!==t),currentSessionId:n.currentSessionId===t?null:n.currentSessionId}));const s=a().sessions;if(!window.CSInterface){localStorage.setItem(C,JSON.stringify(s));return}await T(`saveHistory(${JSON.stringify(s)})`),localStorage.setItem(C,JSON.stringify(s))}catch(s){console.error("Failed to delete session:",s);try{const o=a().sessions;localStorage.setItem(C,JSON.stringify(o))}catch{r({error:s.message||"Failed to delete session"})}}},clearHistory:async()=>{try{if(r({sessions:[],currentSessionId:null}),!window.CSInterface){localStorage.setItem(C,JSON.stringify([]));return}await T("saveHistory([])"),localStorage.setItem(C,JSON.stringify([]))}catch(t){console.error("Failed to clear history:",t);try{localStorage.setItem(C,JSON.stringify([]))}catch{r({error:t.message||"Failed to clear history"})}}},createSession:t=>{const s={id:crypto.randomUUID(),title:t||`Chat ${new Date().toLocaleDateString()}`,messages:[],createdAt:Date.now(),updatedAt:Date.now()};r(n=>({sessions:[s,...n.sessions],currentSessionId:s.id}));const{saveSession:o}=a();return o(s),s},updateSession:(t,s)=>{r(o=>({sessions:o.sessions.map(n=>n.id===t?{...n,...s,updatedAt:Date.now()}:n)}))},setCurrentSession:t=>{r({currentSessionId:t})},getCurrentSession:()=>{const{sessions:t,currentSessionId:s}=a();return t.find(o=>o.id===s)||null},getSessionById:t=>{const{sessions:s}=a();return s.find(o=>o.id===t)||null},getSortedSessions:()=>{const{sessions:t}=a();return[...t].sort((s,o)=>o.updatedAt-s.updatedAt)}})),re=$((r,a)=>({messages:[],isLoading:!1,addMessage:t=>{const s={...t,id:crypto.randomUUID(),timestamp:Date.now()};r(n=>({messages:[...n.messages,s]}));const o=a().currentSession;if(o){const n=()=>{const i=H.getState(),c=i.sessions.find(l=>l.id===o);if(c){const l=a().messages,u={...c,messages:l,updatedAt:Date.now(),title:c.title===`Chat ${new Date(c.createdAt).toLocaleDateString()}`&&l[0]?.content.slice(0,50)+"..."||c.title};i.saveSession(u)}};window.requestIdleCallback?window.requestIdleCallback(n):setTimeout(n,0)}},setLoading:t=>r({isLoading:t}),createNewSession:()=>{const t=crypto.randomUUID();return r({messages:[],currentSession:t}),H.getState().createSession(),t},loadSession:(t,s)=>{r({currentSession:t,messages:s})},clearMessages:()=>{r({messages:[],currentSession:void 0})}})),gt=()=>{const{getActiveProvider:r}=D(),[a,t]=b.useState({isOnline:null,isChecking:!1}),s=r();b.useEffect(()=>{let n,i=!0;const c=async()=>{if(!s?.isConfigured){i&&t({isOnline:null,isChecking:!1});return}i&&t(l=>({...l,isChecking:!0,error:void 0}));try{const l=await ge.checkProviderStatus(s.id,{apiKey:s.apiKey,baseURL:s.baseURL});i&&t({isOnline:l.isOnline,latency:l.latency,isChecking:!1})}catch(l){i&&t({isOnline:!1,isChecking:!1,error:l.message})}};return s?.isConfigured&&(c(),n=setInterval(c,3e4)),()=>{i=!1,n&&clearInterval(n)}},[s]);const o=()=>{const n="w-2.5 h-2.5 rounded-full transition-all duration-300 shadow-sm hover:scale-110 cursor-pointer";return a.isChecking?`${n} bg-adobe-warning animate-pulse shadow-adobe-warning/40`:a.isOnline===!0?`${n} bg-adobe-success shadow-adobe-success/40`:`${n} bg-adobe-error shadow-adobe-error/40`};return e.jsx("div",{className:o()})},ht=()=>{const{getActiveProvider:r,getActiveModel:a,loadSettings:t}=D(),{openModal:s}=_(),{createNewSession:o}=re(),[,n]=b.useState({}),i=r(),c=a(),l=i?.isLoading||!1,u=b.useMemo(()=>i?i.id==="ollama"&&i.isLoading?"Ollama • Loading models...":l?`${i.name} • Loading models...`:c?`${i.name} • ${c.name}`:`${i.name} • Select Model`:"Select AI Provider & Model",[i,c,l]);return b.useEffect(()=>{t()},[t]),b.useEffect(()=>D.subscribe(()=>n({})),[]),e.jsxs("header",{className:"flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>s("status"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all flex items-center justify-center",children:e.jsx(gt,{})}),e.jsxs("button",{onClick:()=>s("provider"),className:"flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group",title:"Select AI Provider & Model",children:[e.jsx("span",{className:"max-w-[300px] truncate",children:u}),l?e.jsx(Z,{size:14,className:"animate-spin text-adobe-text-secondary"}):e.jsx(O,{size:14,className:"text-adobe-text-secondary group-hover:text-adobe-accent transition-colors"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:o,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"New Chat",children:e.jsx(Ee,{size:16})}),e.jsx("div",{className:"h-4 w-px bg-adobe-border"}),e.jsx("button",{onClick:()=>s("chat-history"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Chat History",children:e.jsx(Ce,{size:16})}),e.jsx("button",{onClick:()=>s("settings"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Settings",children:e.jsx(ke,{size:16})})]})]})},pt=["javascript","typescript","jsx","tsx","json","xml","markdown","shell"],ft=["github-dark"],yt=Be({themes:ft,langs:pt});let V=null;async function vt(){return V||(V=await yt),V}function fe(r){return["javascript","typescript","jsx","tsx","json","xml","markdown","shell"].includes(r.toLowerCase())}function jt(r){const a={js:"javascript",ts:"typescript",bash:"shell",sh:"shell",zsh:"shell",fish:"shell",py:"javascript",rb:"javascript",yml:"json",yaml:"json",htm:"json",html:"json",css:"json",sass:"json",scss:"json"},t=r.toLowerCase();return a[t]?a[t]:fe(t)?t:"javascript"}const Nt=({content:r})=>{const[a,t]=b.useState(null),[s,o]=b.useState({});b.useEffect(()=>{vt().then(t)},[]);const n=l=>{o(u=>({...u,[l]:{...u[l],isCollapsed:!u[l]?.isCollapsed}}))},i=async(l,u)=>{try{if(u==="javascript"||u==="jsx"){const m=await T(`
          try {
            ${l}
          } catch (error) {
            return "Error: " + error.toString();
          }
        `);console.log("Code execution result:",m)}else await navigator.clipboard.writeText(l),console.log("Code copied to clipboard for manual execution")}catch(m){console.error("Failed to execute code:",m)}};if(!a)return e.jsx("pre",{className:"whitespace-pre-wrap",children:r});const c=r.split(/(```[\s\S]*?```)/g);return e.jsx(e.Fragment,{children:c.map((l,u)=>{if(l.startsWith("```")){const m=l.split(`
`),p=m[0].replace("```","").trim(),g=m.slice(1,-1).join(`
`),f=fe(p)?p:jt(p),h=`code-block-${u}`,S=s[h]?.isCollapsed||!1,x=["javascript","jsx","typescript","tsx"].includes(f);return e.jsxs("div",{className:"relative border border-adobe-border rounded-lg overflow-hidden bg-adobe-bg-secondary",children:[e.jsxs("div",{className:"flex items-center justify-between px-3 py-2 bg-adobe-bg-tertiary border-b border-adobe-border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-xs font-mono text-adobe-text-secondary uppercase",children:p||"code"}),e.jsxs("span",{className:"text-xs text-adobe-text-secondary",children:[g.split(`
`).length," lines"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{title:"Copy to clipboard",onClick:()=>navigator.clipboard.writeText(g),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:e.jsx(Re,{size:14})}),x&&e.jsx("button",{title:"Run in terminal",onClick:()=>i(g,f),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors text-adobe-accent",children:e.jsx(Te,{size:14})}),e.jsx("button",{title:"Save to file",className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:e.jsx(Ie,{size:14})}),e.jsx("button",{title:S?"Expand":"Collapse",onClick:()=>n(h),className:"p-1.5 hover:bg-adobe-bg-primary/80 rounded text-xs transition-colors",children:S?e.jsx(O,{size:14}):e.jsx(Le,{size:14})})]})]}),!S&&e.jsx("div",{className:"relative",children:e.jsx("div",{className:"overflow-x-auto",dangerouslySetInnerHTML:{__html:a.codeToHtml(g,{lang:f,theme:"github-dark"})}})}),S&&e.jsx("div",{className:"px-3 py-2 text-adobe-text-secondary text-sm italic",children:"Code block collapsed. Click expand to view."})]},u)}return e.jsx("div",{children:l},u)})})},St=({message:r})=>{const a=r.role==="user";return e.jsx("div",{className:`flex gap-3 ${a?"justify-end":"justify-start"} mb-4`,children:e.jsx("div",{className:`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${a?"bg-adobe-bg-tertiary text-adobe-text-primary ml-8 rounded-br-md":"bg-adobe-bg-primary text-adobe-text-primary mr-8 rounded-bl-md border border-adobe-border"}`,children:e.jsx("div",{className:"whitespace-pre-wrap",children:e.jsx(Nt,{content:r.content})})})})};function ye(r,a){let t=null,s=0;return(...o)=>{const n=Date.now();n-s>a?(s=n,r(...o)):(t&&clearTimeout(t),t=setTimeout(()=>{s=Date.now(),r(...o),t=null},a-(n-s)))}}const wt=()=>{const{messages:r,isLoading:a,currentSession:t}=re(),s=b.useRef(null),o=b.useRef(null),[n,i]=b.useState(!1),c=b.useRef();b.useEffect(()=>{const m=o.current,p=s.current;if(!m||!p)return;const{scrollTop:g,scrollHeight:f,clientHeight:h}=m;f-(g+h)<150&&requestAnimationFrame(()=>{p.scrollIntoView({behavior:"smooth"})})},[r.length,a]);const l=b.useCallback(ye(()=>{const m=o.current;if(!m)return;clearTimeout(c.current);const{scrollTop:p,scrollHeight:g,clientHeight:f}=m,h=g-(p+f)<100;i(!h),c.current=setTimeout(()=>{i(!1)},2e3)},100),[]);b.useEffect(()=>{const m=o.current;if(m)return m.addEventListener("scroll",l,{passive:!0}),()=>{m.removeEventListener("scroll",l),clearTimeout(c.current)}},[l]);const u=()=>{s.current?.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{ref:o,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative`,children:[(!t||r.length===0)&&e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3",children:[e.jsx("div",{className:"w-20 h-20 mb-2 flex items-center justify-center",children:e.jsx(W,{size:80,className:"text-adobe-text-secondary"})}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Start a conversation"}),e.jsx("p",{className:"text-sm text-center max-w-md",children:"Type a message below to begin chatting with SahAI"})]}),r.map(m=>e.jsx(St,{message:m},m.id)),a&&e.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:e.jsx("span",{children:"AI is thinking..."})}),e.jsx("div",{ref:s}),n&&e.jsx("button",{onClick:u,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:e.jsx(Pe,{size:18})})]})},ve=b.forwardRef(({value:r,onChange:a,minHeight:t=72,maxHeight:s=200,onHeightChange:o,className:n="",style:i,...c},l)=>{const u=b.useRef(null),m=b.useRef(t),p=b.useCallback(h=>{u.current=h,typeof l=="function"?l(h):l&&(l.current=h)},[l]),g=b.useCallback(()=>{const h=u.current;if(!h)return t;h.style.height="auto";const S=h.scrollHeight,x=Math.min(Math.max(S,t),s);return h.style.height=`${x}px`,x},[t,s]),f=b.useCallback(h=>{a(h),requestAnimationFrame(()=>{const S=g();S!==m.current&&(m.current=S,o?.(S))})},[a,g,o]);return b.useEffect(()=>{!r&&u.current&&(u.current.style.height=`${t}px`,m.current!==t&&(m.current=t,o?.(t)))},[r,t,o]),b.useEffect(()=>{if(u.current){const h=g();m.current=h,o?.(h)}},[g,o]),e.jsx("textarea",{ref:p,value:r,onChange:f,className:`resize-none transition-all duration-150 ease-out ${n}`,style:{minHeight:`${t}px`,maxHeight:`${s}px`,height:`${t}px`,...i},...c})});ve.displayName="AutoResizeTextarea";const Et=le.memo(()=>{const[r,a]=b.useState(""),[t,s]=b.useState(!1),o=b.useRef(null),{addMessage:n,isLoading:i,setLoading:c,currentSession:l,createNewSession:u}=re(),m=4e3,p=!r.trim(),g=r.length>m*.9,f=b.useCallback(y=>{a(y.target.value)},[]),h=b.useCallback(async()=>{const y=r.trim();if(!(!y||i)){a("");try{c(!0),l||u(),n({content:y,role:"user"}),setTimeout(()=>{n({content:`Echo: ${y}`,role:"assistant"}),c(!1)},1e3)}catch{a(y),c(!1)}}},[r,i,l,n,c,u]),S=b.useCallback(y=>{y.key==="Enter"&&!y.shiftKey&&!t&&(y.preventDefault(),h())},[h,t]),x=b.useCallback(()=>{console.log("File attachment clicked - functionality to be implemented")},[]),d=b.useCallback(()=>{console.log("Voice input clicked - functionality to be implemented")},[]);return e.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[e.jsxs("div",{className:"relative flex items-center bg-transparent rounded-lg border border-adobe-text-secondary/50 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[e.jsx("div",{className:"flex items-center pl-3",children:e.jsx("button",{onClick:x,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded",title:"Attach file",disabled:i,children:e.jsx(Ae,{size:18})})}),e.jsx(ve,{ref:o,maxLength:m,value:r,onChange:f,onKeyDown:S,onCompositionStart:()=>s(!0),onCompositionEnd:()=>s(!1),placeholder:"Type a message...",disabled:i,minHeight:72,maxHeight:200,className:"flex-1 bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80 leading-relaxed overflow-y-auto chat-messages-scrollbar"}),e.jsxs("div",{className:"flex items-center pr-3 space-x-1",children:[e.jsx("button",{onClick:d,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40",title:"Voice input",disabled:i,children:e.jsx(Oe,{size:18})}),e.jsx("button",{onClick:h,disabled:p||i,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:i?e.jsx(Z,{size:18,className:"animate-spin"}):e.jsx(Me,{size:18})})]})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[e.jsxs("span",{className:`text-xs ${g?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[r.length,"/",m]}),e.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),Ct=({models:r,value:a,onChange:t,placeholder:s="Search models..."})=>{const[o,n]=b.useState(""),[i,c]=b.useState(!1),[l,u]=b.useState(0),m=b.useRef(null),p=b.useRef(null),g=r.filter(x=>x.name.toLowerCase().includes(o.toLowerCase())||x.id.toLowerCase().includes(o.toLowerCase())),f=r.find(x=>x.id===a),h=b.useCallback(x=>{t(x),c(!1),n(""),u(0)},[t]),S=b.useCallback(x=>{if(!i&&(x.key==="ArrowDown"||x.key==="ArrowUp")){x.preventDefault(),c(!0),n("");return}if(i)switch(x.key){case"ArrowDown":x.preventDefault(),u(d=>d<g.length-1?d+1:d);break;case"ArrowUp":x.preventDefault(),u(d=>d>0?d-1:0);break;case"Enter":x.preventDefault(),g[l]&&h(g[l].id);break;case"Escape":x.preventDefault(),c(!1),u(0);break}},[i,g,l,h]);return b.useEffect(()=>{const x=d=>{m.current&&!m.current.contains(d.target)&&(c(!1),u(0))};return document.addEventListener("mousedown",x),()=>document.removeEventListener("mousedown",x)},[]),b.useEffect(()=>{u(0)},[g]),e.jsxs("div",{className:"relative",ref:m,children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{ref:p,type:"text",value:i?o:f?.name||"",onChange:x=>{n(x.target.value),c(!0),u(0)},onFocus:()=>{c(!0),n(""),u(0)},onKeyDown:S,placeholder:s,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none pr-10",readOnly:!i}),e.jsx("button",{onClick:()=>{i&&g.length>0?h(g[l]?.id||g[0].id):(c(!0),n(""),u(0))},className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",children:e.jsx(ue,{size:18})})]}),i&&e.jsx("div",{className:"absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-lg max-h-52 overflow-auto",children:g.length>0?g.map((x,d)=>e.jsx("div",{className:`px-4 py-2 cursor-pointer ${a===x.id?"bg-adobe-accent text-white":d===l?"bg-adobe-bg-tertiary text-adobe-text-primary":"hover:bg-adobe-bg-tertiary text-adobe-text-primary"}`,onClick:()=>h(x.id),onMouseEnter:()=>u(d),children:x.name},x.id)):e.jsx("div",{className:"px-4 py-2 text-adobe-text-secondary",children:"No models found"})})]})},kt=({providers:r,value:a,onChange:t,placeholder:s="Search providers..."})=>{const[o,n]=b.useState(""),[i,c]=b.useState(!1),[l,u]=b.useState(0),m=b.useRef(null),p=b.useRef(null),g=r.filter(x=>x.name.toLowerCase().includes(o.toLowerCase())||x.id.toLowerCase().includes(o.toLowerCase())).sort((x,d)=>x.isConfigured&&!d.isConfigured?-1:!x.isConfigured&&d.isConfigured?1:x.name.localeCompare(d.name)),f=r.find(x=>x.id===a),h=b.useCallback(x=>{t(x),c(!1),n(""),u(0)},[t]),S=b.useCallback(x=>{if(!i&&(x.key==="ArrowDown"||x.key==="ArrowUp")){x.preventDefault(),c(!0),n("");return}if(i)switch(x.key){case"ArrowDown":x.preventDefault(),u(d=>d<g.length-1?d+1:d);break;case"ArrowUp":x.preventDefault(),u(d=>d>0?d-1:0);break;case"Enter":x.preventDefault(),g[l]&&h(g[l].id);break;case"Escape":x.preventDefault(),c(!1),u(0);break}},[i,g,l,h]);return b.useEffect(()=>{const x=d=>{m.current&&!m.current.contains(d.target)&&(c(!1),u(0))};return document.addEventListener("mousedown",x),()=>document.removeEventListener("mousedown",x)},[]),b.useEffect(()=>{u(0)},[g]),e.jsxs("div",{className:"relative",ref:m,children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{ref:p,type:"text",value:i?o:f?.name||"",onChange:x=>{n(x.target.value),c(!0),u(0)},onFocus:()=>{c(!0),n(""),u(0)},onKeyDown:S,placeholder:s,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all pr-10",readOnly:!i}),e.jsx("button",{onClick:()=>{i&&g.length>0?h(g[l]?.id||g[0].id):(c(!0),n(""),u(0))},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(O,{size:18,className:`transition-transform ${i?"rotate-180":""}`})})]}),i&&e.jsx("div",{className:"absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg shadow-lg max-h-52 overflow-auto",children:g.length>0?g.map((x,d)=>e.jsxs("div",{className:`px-4 py-3 cursor-pointer flex items-center justify-between ${a===x.id?"bg-adobe-accent text-white":d===l?"bg-adobe-bg-tertiary text-adobe-text-primary":"hover:bg-adobe-bg-tertiary text-adobe-text-primary"}`,onClick:()=>h(x.id),onMouseEnter:()=>u(d),children:[e.jsx("span",{children:x.name}),x.isConfigured&&e.jsx("div",{className:`w-2 h-2 rounded-full ${a===x.id?"bg-white":"bg-green-500"}`})]},x.id)):e.jsx("div",{className:"px-4 py-3 text-adobe-text-secondary",children:"No providers found"})})]})};class P extends b.Component{constructor(){super(...arguments);L(this,"state",{hasError:!1})}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,s){console.error("Uncaught error:",t,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 bg-adobe-bg-primary text-adobe-text-primary",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Something went wrong"}),e.jsx("p",{className:"text-adobe-text-secondary mb-4",children:this.state.error?.message||"An unexpected error occurred"}),e.jsx("button",{className:"px-4 py-2 bg-adobe-accent text-white rounded-md hover:bg-adobe-accent/90",onClick:()=>this.setState({hasError:!1,error:void 0}),children:"Try again"})]}):this.props.children}}class Rt extends b.Component{constructor(){super(...arguments);L(this,"retryTimeout");L(this,"state",{hasError:!1,errorCount:0});L(this,"handleRetry",()=>{this.setState({hasError:!1,error:void 0}),this.props.onRetry&&this.props.onRetry()})}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,s){if(console.error(`Provider ${this.props.providerId} error:`,t,s),this.setState(o=>({errorCount:o.errorCount+1})),this.state.errorCount>=3){console.warn(`Provider ${this.props.providerId} has failed ${this.state.errorCount} times, stopping auto-retry`);return}this.state.errorCount<2&&this.props.providerId!=="ollama"&&(this.retryTimeout=setTimeout(()=>{this.handleRetry()},2e3*(this.state.errorCount+1)))}componentWillUnmount(){this.retryTimeout&&window.clearTimeout(this.retryTimeout)}render(){if(this.state.hasError){const t=this.state.errorCount>=3;return e.jsxs("div",{className:"flex flex-col items-center justify-center p-6 bg-adobe-bg-secondary border border-adobe-border rounded-lg",children:[e.jsx(me,{size:32,className:"text-adobe-warning mb-3"}),e.jsx("h3",{className:"text-lg font-semibold text-adobe-text-primary mb-2",children:"Provider Error"}),e.jsx("p",{className:"text-adobe-text-secondary text-sm text-center mb-4",children:t?`${this.props.providerId} has encountered multiple errors. Please check your configuration.`:`There was an error loading ${this.props.providerId}. ${this.state.errorCount<2?"Retrying automatically...":""}`}),this.state.error&&e.jsxs("details",{className:"mb-4 w-full",children:[e.jsx("summary",{className:"text-xs text-adobe-text-secondary cursor-pointer hover:text-adobe-text-primary",children:"Error Details"}),e.jsx("pre",{className:"text-xs text-adobe-error mt-2 p-2 bg-adobe-bg-primary rounded border overflow-auto max-h-20",children:this.state.error.message})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:this.handleRetry,className:"flex items-center gap-2 px-3 py-1.5 bg-adobe-accent text-white rounded text-sm hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(ee,{size:14}),"Try Again"]}),t&&e.jsx("button",{onClick:()=>this.setState({hasError:!1,error:void 0,errorCount:0}),className:"px-3 py-1.5 border border-adobe-border text-adobe-text-primary rounded text-sm hover:bg-adobe-bg-tertiary transition-colors",children:"Reset"})]}),e.jsxs("p",{className:"text-xs text-adobe-text-secondary mt-3 text-center",children:["Error count: ",this.state.errorCount,"/3"]})]})}return this.props.children}}const Tt=()=>{const{closeModal:r}=_(),{providers:a,activeProviderId:t,saveProviderSelection:s,loadModelsForProvider:o,updateProviderConfig:n,setSelectedModel:i}=D(),[c,l]=b.useState(t||""),[u,m]=b.useState(""),[p,g]=b.useState(""),[f,h]=b.useState(""),[S,x]=b.useState(!1),d=a.find(N=>N.id===c);b.useEffect(()=>{d&&(m(d.apiKey||""),g(d.baseURL||""),h(d.selectedModelId||""))},[d]);const y=N=>{l(N);const R=a.find(F=>F.id===N);R?(m(R.apiKey||""),g(R.baseURL||""),h(R.selectedModelId||"")):(m(""),g(""),h(""))},j=N=>{d&&(d.configType==="apiKey"?m(N):g(N))},v=b.useCallback(ye(()=>{if(!c||!d||c==="ollama")return;(d.configType==="apiKey"?!!u.trim():!!p.trim())&&o(c)},1e3),[c,d,u,p]);b.useEffect(()=>{if(!d)return;if(d.configType==="apiKey"&&u!==(d.apiKey||"")||d.configType==="baseURL"&&p!==(d.baseURL||"")){const R={};d.configType==="apiKey"?R.apiKey=u:R.baseURL=p,n(c,R),v()}f&&f!==d.selectedModelId&&i(c,f)},[u,p,f,c,d,n,v,i]);const w=()=>{if(c){if(d?.configType==="apiKey"&&!u.trim()){k.error("Configuration Error","Please enter your API key first");return}if(d?.configType==="baseURL"&&!p.trim()){k.error("Configuration Error","Please enter the base URL first");return}o(c)}},E=async()=>{if(!(!c||!d||S)){x(!0);try{const N={selectedModelId:f};if(d.configType==="apiKey"?N.apiKey=u:N.baseURL=p,!(d.configType==="apiKey"?!!N.apiKey?.trim():!!N.baseURL?.trim())){k.error("Configuration Error",`Please enter your ${d.configType==="apiKey"?"API key":"base URL"}`),x(!1);return}if(!N.selectedModelId){k.error("Configuration Error","Please select a model"),x(!1);return}if(d.id!=="ollama")try{if(await o(c),!a.find(q=>q.id===c)?.models.find(q=>q.id===f)){k.error("Configuration Error","Selected model is not available"),x(!1);return}}catch(F){k.error("Configuration Error","Failed to validate provider configuration: "+F.message),x(!1);return}await s(c,N),r()}finally{x(!1)}}},ae=d?.models.map(N=>({id:N.id,name:N.name}))||[],je=a.map(N=>({id:N.id,name:N.name,isConfigured:N.isConfigured}));return e.jsx(P,{children:e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-xl w-[600px] shadow-2xl",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-6 rounded-t-xl",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-adobe-text-primary",children:"Configure AI Provider"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1",children:"Select and configure your AI provider"})]}),e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors p-1 hover:bg-adobe-bg-tertiary rounded-lg",children:e.jsx(M,{size:20})})]})}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:"Choose Provider"}),e.jsx(kt,{providers:je,value:c,onChange:y,placeholder:"Search and select a provider..."})]}),d&&e.jsxs(Rt,{providerId:d.id,onRetry:w,children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary mb-2",children:d.configType==="apiKey"?"API Key":"Base URL"}),e.jsx("input",{type:d.configType==="apiKey"?"password":"text",placeholder:d.configType==="apiKey"?`Enter your ${d.name} API key...`:`Enter ${d.name} base URL...`,value:d.configType==="apiKey"?u:p,onChange:N=>j(N.target.value),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("label",{className:"block text-sm font-semibold text-adobe-text-primary",children:"Select Model"}),d.id==="ollama"&&e.jsx("button",{onClick:w,disabled:d.isLoading,className:"text-xs text-adobe-accent hover:text-adobe-accent/80 disabled:opacity-50",children:d.isLoading?"Loading...":"Load Models"})]}),d.isLoading?e.jsxs("div",{className:"flex items-center space-x-2 text-adobe-text-secondary p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-adobe-accent"}),e.jsx("span",{children:"Loading models..."})]}):d.error?e.jsxs("div",{className:"p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm",children:[d.error,e.jsx("button",{onClick:w,className:"ml-2 text-red-300 hover:text-red-200 underline",children:"Retry"})]}):ae.length>0?e.jsx(Ct,{models:ae,value:f,onChange:h,placeholder:`Search ${d.name} models...`}):e.jsx("div",{className:"p-4 bg-adobe-bg-secondary rounded-lg border border-adobe-border text-adobe-text-secondary text-sm",children:d.id==="ollama"?'Click "Load Models" to fetch available Ollama models':"No models available. Configure the provider first."})]})]})]}),d&&e.jsx("div",{className:"border-t border-adobe-border p-6 bg-adobe-bg-secondary rounded-b-xl",children:e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:r,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-lg transition-all",children:"Cancel"}),e.jsx("button",{onClick:E,disabled:!c||!d||S||d.configType==="apiKey"&&!u.trim()||d.configType==="baseURL"&&!p.trim()||!f,className:"px-6 py-2 bg-adobe-accent text-white rounded-lg hover:bg-adobe-accent/90 transition-all disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm",children:S?"Saving...":"Save & Configure"})]})})]})})})},It=()=>{const{closeModal:r}=_(),{sessions:a}=H(),[t,s]=b.useState({theme:"auto",autoSave:!0,showNotifications:!0,maxHistoryItems:100,debugMode:!1}),[o,n]=b.useState("settings"),[i,c]=b.useState(!1),[l,u]=b.useState(!0),[m,p]=b.useState("30d"),[g,f]=b.useState(!1);b.useEffect(()=>{(async()=>{try{const v=await A.load();v&&typeof v=="object"&&s(v)}catch(v){console.error("Failed to load settings:",v)}finally{u(!1)}})()},[]);const h=[{id:"settings",title:"General Settings",description:"Configure application preferences",icon:e.jsx(oe,{size:16,className:"text-adobe-accent"})},{id:"analytics",title:"Analytics",description:"View usage statistics",icon:e.jsx(De,{size:16,className:"text-adobe-accent"})},{id:"help",title:"Help & Support",description:"Get help and answers",icon:e.jsx(_e,{size:16,className:"text-adobe-accent"})},{id:"about",title:"About",description:"About SahAI Extension",icon:e.jsx(Y,{size:16,className:"text-adobe-accent"})}],x=(()=>{const j=Date.now(),v=a.filter(w=>{if(m==="all")return!0;const E=parseInt(m.replace("d",""));return j-w.createdAt<=E*24*60*60*1e3});return{messages:v.reduce((w,E)=>w+E.messages.length,0),sessions:v.length,tokens:v.reduce((w,E)=>w+(E.tokenCount||0),0),cost:v.reduce((w,E)=>w+(E.cost||0),0),avgLatency:v.length>0?v.reduce((w,E)=>w+(E.avgLatency||0),0)/v.length:0}})(),d=()=>{f(!0),setTimeout(()=>f(!1),800)},y=async()=>{c(!0);try{const v={...await A.load(),appSettings:t};await A.save(v),r()}catch(j){console.error("Failed to save settings:",j)}finally{c(!1)}};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Settings"}),e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(M,{size:20})})]})}),e.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[e.jsx("div",{className:"w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto",children:e.jsx("div",{className:"space-y-1",children:h.map(j=>e.jsx("button",{onClick:()=>n(j.id),className:`w-full text-left p-3 rounded-md transition-colors ${o===j.id?"bg-adobe-accent/10 text-adobe-text-primary":"text-adobe-text-secondary hover:bg-adobe-bg-tertiary"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-1.5 rounded-md bg-adobe-bg-tertiary",children:j.icon}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:j.title}),e.jsx("div",{className:"text-xs mt-1",children:j.description})]})]})},j.id))})}),e.jsx("div",{className:"w-2/3 p-6 overflow-y-auto",children:l?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):o==="settings"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Appearance"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:"Theme"}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:t.theme,onChange:j=>s(v=>({...v,theme:j.target.value})),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none",children:[e.jsx("option",{value:"auto",children:"Auto (System)"}),e.jsx("option",{value:"light",children:"Light"}),e.jsx("option",{value:"dark",children:"Dark"})]}),e.jsx(O,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"General"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.autoSave,onChange:j=>s(v=>({...v,autoSave:j.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Auto-save conversations"})]}),e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.showNotifications,onChange:j=>s(v=>({...v,showNotifications:j.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Show notifications"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:["Max history items (",t.maxHistoryItems,")"]}),e.jsx("input",{type:"range",min:"10",max:"500",step:"10",value:t.maxHistoryItems,onChange:j=>s(v=>({...v,maxHistoryItems:parseInt(j.target.value)})),className:"w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Advanced"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("label",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:t.debugMode,onChange:j=>s(v=>({...v,debugMode:j.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),e.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Debug mode"})]})})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:y,disabled:i,className:"flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:[e.jsx(oe,{size:16}),e.jsx("span",{children:i?"Saving...":"Save Settings"})]})})]}):o==="analytics"?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Analytics"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:m,onChange:j=>p(j.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-1.5 pr-8 text-sm text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"7d",children:"Last 7 days"}),e.jsx("option",{value:"30d",children:"Last 30 days"}),e.jsx("option",{value:"90d",children:"Last 90 days"}),e.jsx("option",{value:"all",children:"All time"})]}),e.jsx(O,{size:14,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]}),e.jsx("button",{onClick:d,disabled:g,className:"p-1.5 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(ee,{size:16,className:g?"animate-spin":""})})]})]}),g?e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[...Array(4)].map((j,v)=>e.jsx("div",{className:"h-24 bg-adobe-bg-secondary rounded-md animate-pulse"},v))}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Messages"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:x.messages.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Sessions"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:x.sessions.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Tokens Used"}),e.jsx("div",{className:"text-2xl font-medium text-adobe-text-primary",children:x.tokens.toLocaleString()})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-1",children:"Est. Cost"}),e.jsxs("div",{className:"text-2xl font-medium text-adobe-text-primary",children:["$",x.cost.toFixed(4)]})]})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Average Latency"}),e.jsxs("div",{className:"text-sm font-medium text-adobe-text-primary",children:[x.avgLatency.toFixed(2)," seconds"]})]}),e.jsx("div",{className:"h-2 bg-adobe-bg-tertiary rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-adobe-accent transition-all duration-300",style:{width:`${Math.min(100,x.avgLatency*50)}%`}})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-md",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary mb-2",children:"Performance Tips"}),e.jsxs("ul",{className:"text-sm text-adobe-text-secondary space-y-1",children:[e.jsx("li",{children:"• Use concise prompts to reduce token usage"}),e.jsx("li",{children:"• Select faster models for simple tasks"}),e.jsx("li",{children:"• Monitor usage to optimize costs"})]})]})]})]}):o==="help"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"Help & Support"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Documentation"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Read our comprehensive documentation for detailed guides."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"FAQ"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Find answers to frequently asked questions."})]}),e.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[e.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Contact Support"}),e.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Email <NAME_EMAIL> for assistance."})]})]})]}):e.jsx("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Y,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"About SahAI"}),e.jsx("p",{className:"text-sm mb-4",children:"Version 2.0.0"}),e.jsx("p",{className:"text-sm",children:"AI-powered assistant for Adobe Creative Suite"})]})})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-3 text-xs text-adobe-text-secondary text-center",children:[o==="settings"&&"General Settings",o==="analytics"&&"Usage Analytics",o==="help"&&"Help & Support",o==="about"&&"About SahAI"]})]})})},Lt=()=>{const{closeModal:r}=_(),{sessions:a,isLoading:t,error:s,loadHistory:o,deleteSession:n,getSortedSessions:i}=H(),[c,l]=b.useState(""),[u,m]=b.useState(null),[p,g]=b.useState("recent");b.useEffect(()=>{o()},[o]);const f=i().filter(d=>d.title.toLowerCase().includes(c.toLowerCase())||d.messages.some(y=>y.content.toLowerCase().includes(c.toLowerCase()))).sort((d,y)=>p==="alphabetical"?d.title.localeCompare(y.title):p==="oldest"?d.createdAt-y.createdAt:y.createdAt-d.createdAt),h=async(d,y)=>{y.stopPropagation(),confirm("Are you sure you want to delete this chat session?")&&await n(d)},S=d=>{const y=new Date(d),v=(new Date().getTime()-y.getTime())/(1e3*60*60);return v<24?y.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):v<24*7?y.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):y.toLocaleDateString([],{month:"short",day:"numeric",year:"numeric"})},x=d=>{const y=d.messages[d.messages.length-1];if(!y)return"No messages";const j=y.content.slice(0,100);return j.length<y.content.length?`${j}...`:j};return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Chat History"}),e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(M,{size:20})})]})}),e.jsx("div",{className:"p-4 border-b border-adobe-border",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:c,onChange:d=>l(d.target.value),placeholder:"Search chat history...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none text-sm"}),e.jsx("button",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",onClick:()=>l(""),children:c?e.jsx(M,{size:16}):e.jsx(ue,{size:16})})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:p,onChange:d=>g(d.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pl-3 pr-8 text-adobe-text-primary text-sm focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[e.jsx("option",{value:"recent",children:"Most Recent"}),e.jsx("option",{value:"oldest",children:"Oldest First"}),e.jsx("option",{value:"alphabetical",children:"Alphabetical"})]}),e.jsx(O,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:t?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):s?e.jsxs("div",{className:"p-4 text-center",children:[e.jsxs("div",{className:"bg-red-900/20 border border-red-800/50 rounded-lg p-4 mb-4",children:[e.jsx("p",{className:"text-red-400 font-medium mb-2",children:"Error loading history:"}),e.jsx("p",{className:"text-sm text-red-300",children:s})]}),e.jsx("button",{onClick:o,className:"px-4 py-2 bg-adobe-accent hover:bg-adobe-accent-hover text-white rounded-md transition-colors",children:"Retry Loading History"})]}):f.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(W,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:c?"No matching sessions found":"No chat history yet"}),e.jsx("p",{className:"text-sm",children:c?"Try a different search term":"Start a new conversation to see it here"})]}):e.jsx("div",{className:"h-full overflow-y-auto p-2 space-y-2",children:f.map(d=>e.jsxs("div",{className:`p-3 rounded-md cursor-pointer transition-colors ${u?.id===d.id?"bg-adobe-accent/10 border-l-2 border-adobe-accent":"bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary"}`,onClick:()=>m(d),children:[e.jsxs("div",{className:"flex justify-between items-start gap-2",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary truncate",children:d.title}),e.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1 line-clamp-2",children:x(d)})]}),e.jsx("button",{onClick:y=>h(d.id,y),className:"text-adobe-text-secondary hover:text-adobe-error transition-colors p-1",title:"Delete session",children:e.jsx(Ue,{size:14})})]}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx(W,{size:12}),e.jsxs("span",{children:[d.messages.length," messages"]})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[e.jsx($e,{size:12}),e.jsx("span",{children:S(d.createdAt)})]})]})]},d.id))})}),e.jsxs("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center",children:["Showing ",f.length," of ",a.length," chat sessions"]})]})})},Pt=()=>{const{closeModal:r}=_(),{getActiveProvider:a}=D(),[t,s]=b.useState({isOnline:null,isChecking:!1}),o=a(),n=async()=>{if(!o?.isConfigured){s({isOnline:null,isChecking:!1});return}s(m=>({...m,isChecking:!0,error:void 0}));try{const m=await ge.checkProviderStatus(o.id,{apiKey:o.apiKey,baseURL:o.baseURL});s({isOnline:m.isOnline,latency:m.latency,isChecking:!1,lastChecked:Date.now()})}catch(m){s({isOnline:!1,isChecking:!1,error:m.message,lastChecked:Date.now()})}};b.useEffect(()=>{n();const m=setInterval(n,3e4);return()=>clearInterval(m)},[o]);const i=()=>t.isChecking?e.jsx(Z,{size:20,className:"animate-spin text-yellow-500"}):t.isOnline===!0?e.jsx(Fe,{size:20,className:"text-green-500"}):t.isOnline===!1?e.jsx(ze,{size:20,className:"text-red-500"}):e.jsx(Q,{size:20,className:"text-gray-500"}),c=()=>t.isChecking?"Checking connection...":t.isOnline===!0?"Online":t.isOnline===!1?"Offline":"Unknown",l=()=>t.isChecking?"text-yellow-600":t.isOnline===!0?"text-green-600":t.isOnline===!1?"text-red-600":"text-gray-600",u=()=>t.isOnline===!0?"good":t.isOnline===!1?"critical":"warning";return e.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:e.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[e.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Provider Status"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>n(),disabled:t.isChecking,className:"p-1 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded transition-colors disabled:opacity-50",title:"Refresh status",children:e.jsx(ee,{size:18,className:t.isChecking?"animate-spin":""})}),e.jsx("button",{onClick:r,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(M,{size:20})})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-4",children:o?e.jsxs("div",{className:"h-full flex flex-col gap-4",children:[e.jsx("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex-shrink-0",children:i()}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-adobe-text-primary",children:o.name}),e.jsx("p",{className:`text-sm font-medium ${l()}`,children:c()})]})]}),e.jsx("div",{className:`text-xs px-2 py-1 rounded ${u()==="good"?"bg-green-900/30 text-green-500":u()==="warning"?"bg-yellow-900/30 text-yellow-500":"bg-red-900/30 text-red-500"}`,children:c()})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-adobe-text-secondary",children:"Latency"}),e.jsx("span",{className:"text-xs text-adobe-text-tertiary",children:"Lower is better"})]}),e.jsx("div",{className:"mt-2",children:t.latency?e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsxs("span",{className:"text-2xl font-medium text-adobe-text-primary",children:[t.latency,"ms"]}),e.jsx("span",{className:`text-xs mb-1 ${t.latency<100?"text-green-500":t.latency<300?"text-yellow-500":"text-red-500"}`,children:t.latency<100?"Excellent":t.latency<300?"Good":"Poor"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Last Checked"}),e.jsx("div",{className:"mt-2",children:t.lastChecked?e.jsxs("div",{className:"text-adobe-text-primary",children:[e.jsx("div",{className:"text-xl font-medium",children:new Date(t.lastChecked).toLocaleTimeString()}),e.jsx("div",{className:"text-xs text-adobe-text-tertiary mt-1",children:new Date(t.lastChecked).toLocaleDateString()})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"--"})})]}),e.jsxs("div",{className:"bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border col-span-2",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary",children:"Endpoint"}),e.jsx("div",{className:"mt-2",children:o.baseURL?e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"truncate text-adobe-text-primary font-mono text-sm",children:o.baseURL}),e.jsx("button",{className:"text-xs text-adobe-accent hover:text-adobe-accent-hover",onClick:()=>navigator.clipboard.writeText(o.baseURL||""),children:"Copy"})]}):e.jsx("span",{className:"text-adobe-text-tertiary",children:"Not configured"})})]})]}),e.jsxs("div",{className:"flex-1 bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border overflow-auto",children:[e.jsx("div",{className:"text-sm text-adobe-text-secondary mb-2",children:"Status Details"}),t.error?e.jsxs("div",{className:"p-3 bg-red-900/20 border border-red-800/50 rounded text-sm text-red-400",children:[e.jsx("div",{className:"font-medium mb-1",children:"Error:"}),e.jsx("div",{children:t.error})]}):e.jsx("div",{className:"text-sm text-adobe-text-primary",children:t.isChecking?"Checking provider status...":t.isOnline===!0?"Provider is online and responding normally.":t.isOnline===!1?"Provider is offline or not responding to requests.":"Provider status unknown. Please check configuration."})]})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[e.jsx(Q,{size:48,className:"opacity-50"}),e.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"No provider selected"}),e.jsx("p",{className:"text-sm",children:"Select a provider to check connection status"})]})}),e.jsx("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center bg-adobe-bg-secondary",children:"Status checks are performed automatically every 30 seconds"})]})})},At=()=>{const{modal:r}=_();if(!r)return null;switch(r){case"provider":return e.jsx(Tt,{});case"settings":return e.jsx(It,{});case"chat-history":return e.jsx(Lt,{});case"status":return e.jsx(Pt,{});default:return null}},Ot=({toast:r})=>{const{removeToast:a}=I(),[t,s]=b.useState(!1),o=()=>{s(!0),setTimeout(()=>a(r.id),200)};b.useEffect(()=>{const c=setTimeout(()=>{o()},r.duration||4e3);return()=>clearTimeout(c)},[r.duration]);const n=()=>{switch(r.type){case"success":return e.jsx(Ke,{size:20,className:"text-adobe-success"});case"error":return e.jsx(Q,{size:20,className:"text-adobe-error"});case"warning":return e.jsx(me,{size:20,className:"text-adobe-warning"});case"info":default:return e.jsx(Y,{size:20,className:"text-adobe-accent"})}},i=()=>{switch(r.type){case"success":return"border-l-adobe-success";case"error":return"border-l-adobe-error";case"warning":return"border-l-adobe-warning";case"info":default:return"border-l-adobe-accent"}};return e.jsx("div",{className:`
        transform transition-all duration-200 ease-in-out
        ${t?"translate-x-full opacity-0":"translate-x-0 opacity-100"}
        bg-adobe-bg-secondary border border-adobe-border ${i()} border-l-4
        rounded-md shadow-lg p-4 mb-3 max-w-sm w-full
      `,children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 mt-0.5",children:n()}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-adobe-text-primary truncate",children:r.title}),r.message&&e.jsx("p",{className:"text-xs text-adobe-text-secondary mt-1 break-words",children:r.message})]}),e.jsx("button",{onClick:o,className:"flex-shrink-0 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:e.jsx(M,{size:16})})]})})},Mt=()=>{const{toasts:r}=I();return r.length===0?null:e.jsx("div",{className:"fixed top-4 right-4 z-[9999] pointer-events-none",children:e.jsx("div",{className:"space-y-2 pointer-events-auto",children:r.map(a=>e.jsx(Ot,{toast:a},a.id))})})},Dt=()=>e.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[e.jsx(P,{children:e.jsx(ht,{})}),e.jsx(P,{children:e.jsx(wt,{})}),e.jsx(P,{children:e.jsx(Et,{})}),e.jsx(P,{children:e.jsx(At,{})}),e.jsx(P,{children:e.jsx(Mt,{})})]});it();I.getState();D.getState().loadSettings().then(()=>{X.createRoot(document.getElementById("root")).render(e.jsx(le.StrictMode,{children:e.jsx(Dt,{})}))});export{Kt as D,Gt as E,qt as L,Bt as M,Ht as S};
